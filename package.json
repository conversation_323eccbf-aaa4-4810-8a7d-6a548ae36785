{"name": "vicky-mosafan-portfolio", "private": true, "version": "1.0.0", "type": "module", "description": "Modern fullstack developer portfolio with terminal theme and interactive 3D elements", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["portfolio", "react", "typescript", "framer-motion", "tailwind", "terminal", "3d", "interactive"], "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "rm -rf dist node_modules/.vite", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "motion": "^10.18.0", "lucide-react": "^0.451.0", "sonner": "2.0.3", "react-hook-form": "7.55.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/node": "^22.8.4", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^4.0.0", "@tailwindcss/typography": "^0.5.15", "typescript": "^5.6.3", "vite": "^5.4.8", "vite-plugin-eslint": "^1.8.1", "vite-bundle-analyzer": "^0.11.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "repository": {"type": "git", "url": "https://github.com/vickymosafan/portfolio.git"}, "bugs": {"url": "https://github.com/vickymosafan/portfolio/issues"}, "homepage": "https://vickymosafan.dev"}