import { 
  Code2, 
  Server, 
  Database, 
  Globe, 
  Smartphone, 
  Cloud,
  Coffee,
  GitBranch
} from 'lucide-react';
import { Skill, SkillCategory, Project, Stat } from '../types';

// Skills data
export const skillCategories: SkillCategory[] = [
  {
    name: 'Frontend',
    icon: Code2,
    color: 'text-blue-400',
    skills: [
      { name: 'React', level: 90, category: 'Frontend', color: '#61DAFB' },
      { name: 'TypeScript', level: 85, category: 'Frontend', color: '#3178C6' },
      { name: 'Next.js', level: 88, category: 'Frontend', color: '#000000' },
      { name: 'Tailwind CSS', level: 92, category: 'Frontend', color: '#06B6D4' },
      { name: 'JavaScript', level: 95, category: 'Frontend', color: '#F7DF1E' },
      { name: 'HTML/CSS', level: 95, category: 'Frontend', color: '#E34F26' },
    ]
  },
  {
    name: 'Backend',
    icon: Server,
    color: 'text-green-400',
    skills: [
      { name: 'Node.js', level: 88, category: 'Backend', color: '#339933' },
      { name: 'Express.js', level: 85, category: 'Backend', color: '#000000' },
      { name: 'Python', level: 80, category: 'Backend', color: '#3776AB' },
      { name: 'REST APIs', level: 90, category: 'Backend', color: '#FF6B35' },
      { name: 'GraphQL', level: 75, category: 'Backend', color: '#E10098' },
    ]
  },
  {
    name: 'Database',
    icon: Database,
    color: 'text-purple-400',
    skills: [
      { name: 'MongoDB', level: 85, category: 'Database', color: '#47A248' },
      { name: 'PostgreSQL', level: 80, category: 'Database', color: '#336791' },
      { name: 'Redis', level: 75, category: 'Database', color: '#DC382D' },
      { name: 'Prisma', level: 82, category: 'Database', color: '#2D3748' },
    ]
  },
  {
    name: 'Tools & DevOps',
    icon: Cloud,
    color: 'text-orange-400',
    skills: [
      { name: 'Git', level: 90, category: 'Tools', color: '#F05032' },
      { name: 'Docker', level: 78, category: 'Tools', color: '#2496ED' },
      { name: 'AWS', level: 70, category: 'Tools', color: '#FF9900' },
      { name: 'Vercel', level: 85, category: 'Tools', color: '#000000' },
      { name: 'Linux', level: 75, category: 'Tools', color: '#FCC624' },
    ]
  }
];

// Projects data
export const projects: Project[] = [
  {
    id: 'ecommerce-platform',
    title: 'E-Commerce Platform',
    description: 'Full-stack e-commerce solution with React, Node.js, and Stripe integration',
    longDescription: 'A comprehensive e-commerce platform built from scratch with modern technologies. Features include user authentication, product catalog, shopping cart, payment processing, and admin dashboard.',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Stripe', 'JWT'],
    category: 'Full-Stack',
    status: 'completed',
    links: {
      live: 'https://ecommerce-demo.vercel.app',
      github: 'https://github.com/vicky/ecommerce-platform'
    },
    features: [
      'User authentication & authorization',
      'Product catalog with search & filters',
      'Shopping cart & checkout process',
      'Payment integration with Stripe',
      'Admin dashboard for inventory management',
      'Order tracking & history',
      'Responsive design for all devices'
    ],
    challenges: [
      'Implementing secure payment processing',
      'Optimizing database queries for large product catalogs',
      'Creating responsive design for mobile commerce'
    ],
    learnings: [
      'Advanced React patterns and state management',
      'Payment gateway integration best practices',
      'Database optimization techniques'
    ]
  },
  {
    id: 'task-management-app',
    title: 'Task Management App',
    description: 'Collaborative task management tool with real-time updates and team features',
    longDescription: 'A modern task management application designed for teams to collaborate effectively. Built with React and real-time WebSocket connections for instant updates.',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'TypeScript', 'Socket.io', 'Node.js', 'PostgreSQL'],
    category: 'Web App',
    status: 'completed',
    links: {
      live: 'https://taskflow-app.vercel.app',
      github: 'https://github.com/vicky/task-management'
    },
    features: [
      'Real-time collaboration',
      'Drag & drop task organization',
      'Team member management',
      'Project timelines & deadlines',
      'File attachments & comments',
      'Progress tracking & analytics'
    ]
  },
  {
    id: 'weather-dashboard',
    title: 'Weather Dashboard',
    description: 'Interactive weather dashboard with maps, forecasts, and location-based data',
    longDescription: 'A comprehensive weather application that provides detailed weather information, forecasts, and interactive maps for any location worldwide.',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'TypeScript', 'OpenWeather API', 'Mapbox', 'Chart.js'],
    category: 'Frontend',
    status: 'completed',
    links: {
      live: 'https://weather-dash.vercel.app',
      github: 'https://github.com/vicky/weather-dashboard'
    },
    features: [
      'Current weather conditions',
      '7-day weather forecast',
      'Interactive weather maps',
      'Location-based auto-detection',
      'Weather alerts & notifications',
      'Historical weather data charts'
    ]
  },
  {
    id: 'chat-application',
    title: 'Real-time Chat App',
    description: 'Modern chat application with real-time messaging, file sharing, and group chats',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'Socket.io', 'Node.js', 'MongoDB', 'Cloudinary'],
    category: 'Full-Stack',
    status: 'in-progress',
    links: {
      github: 'https://github.com/vicky/chat-app'
    },
    features: [
      'Real-time messaging',
      'Group chat creation',
      'File and image sharing',
      'Message encryption',
      'Online status indicators',
      'Message search & history'
    ]
  },
  {
    id: 'portfolio-website',
    title: 'Portfolio Website',
    description: 'Personal portfolio website with modern design and interactive animations',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
    category: 'Frontend',
    status: 'completed',
    links: {
      live: 'https://vicky-mosafan.vercel.app',
      github: 'https://github.com/vicky/portfolio'
    },
    features: [
      'Responsive design',
      'Interactive animations',
      'Contact form integration',
      'SEO optimized',
      'Fast loading performance'
    ]
  },
  {
    id: 'api-gateway',
    title: 'Microservices API Gateway',
    description: 'Scalable API gateway for microservices architecture with rate limiting and monitoring',
    image: '/api/placeholder/600/400',
    technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Nginx'],
    category: 'Backend',
    status: 'planned',
    links: {
      github: 'https://github.com/vicky/api-gateway'
    },
    features: [
      'Request routing & load balancing',
      'Rate limiting & throttling',
      'Authentication & authorization',
      'Request/response monitoring',
      'Circuit breaker pattern',
      'Containerized deployment'
    ]
  }
];

// Stats data
export const stats: Stat[] = [
  {
    icon: Coffee,
    label: 'Projects Completed',
    value: '15+',
    color: 'text-green-400',
    bgColor: 'bg-green-400/10',
    description: 'Successfully delivered projects across various technologies'
  },
  {
    icon: GitBranch,
    label: 'Years Experience',
    value: '2+',
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/10',
    description: 'Years of professional development experience'
  }
];

// Tech stack for hero section
export const techStack = [
  { 
    name: 'Frontend', 
    icon: Code2, 
    color: 'text-blue-400', 
    bgColor: 'bg-blue-400/10',
    technologies: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS']
  },
  { 
    name: 'Backend', 
    icon: Server, 
    color: 'text-green-400', 
    bgColor: 'bg-green-400/10',
    technologies: ['Node.js', 'Express', 'Python', 'REST APIs']
  },
  { 
    name: 'Database', 
    icon: Database, 
    color: 'text-purple-400', 
    bgColor: 'bg-purple-400/10',
    technologies: ['MongoDB', 'PostgreSQL', 'Redis', 'Prisma']
  },
  { 
    name: 'Mobile', 
    icon: Smartphone, 
    color: 'text-pink-400', 
    bgColor: 'bg-pink-400/10',
    technologies: ['React Native', 'Expo', 'Flutter', 'Progressive Web Apps']
  }
];

// Filter categories for projects
export const projectCategories = [
  'All',
  'Full-Stack',
  'Frontend',
  'Backend',
  'Mobile',
  'Web App'
];

// Social links
export const socialLinks = [
  {
    name: 'GitHub',
    url: 'https://github.com/vickymosafan',
    icon: 'github',
    color: 'text-gray-400'
  },
  {
    name: 'LinkedIn',
    url: 'https://linkedin.com/in/vickymosafan',
    icon: 'linkedin',
    color: 'text-blue-400'
  },
  {
    name: 'Twitter',
    url: 'https://twitter.com/vickymosafan',
    icon: 'twitter',
    color: 'text-sky-400'
  },
  {
    name: 'Email',
    url: 'mailto:<EMAIL>',
    icon: 'mail',
    color: 'text-green-400'
  }
];

// Experience timeline
export const experience = [
  {
    id: 'freelance-2023',
    title: 'Freelance Full-Stack Developer',
    company: 'Self-Employed',
    period: '2023 - Present',
    location: 'Remote',
    description: 'Building custom web applications for clients using modern technologies like React, Node.js, and cloud services.',
    achievements: [
      'Delivered 10+ successful projects',
      'Maintained 100% client satisfaction rate',
      'Specialized in e-commerce and SaaS solutions'
    ],
    technologies: ['React', 'Node.js', 'MongoDB', 'AWS', 'Stripe']
  },
  {
    id: 'company-2022',
    title: 'Frontend Developer',
    company: 'Tech Solutions Inc.',
    period: '2022 - 2023',
    location: 'Jakarta, Indonesia',
    description: 'Developed and maintained multiple client-facing applications, focusing on performance optimization and user experience.',
    achievements: [
      'Improved app performance by 40%',
      'Led UI/UX redesign project',
      'Mentored junior developers'
    ],
    technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Jest']
  },
  {
    id: 'internship-2021',
    title: 'Web Development Intern',
    company: 'StartupXYZ',
    period: '2021 - 2022',
    location: 'Bandung, Indonesia',
    description: 'Gained hands-on experience in full-stack development, working on various features for the company\'s main product.',
    achievements: [
      'Built responsive landing pages',
      'Implemented REST API endpoints',
      'Participated in agile development process'
    ],
    technologies: ['HTML', 'CSS', 'JavaScript', 'PHP', 'MySQL']
  }
];

// Education
export const education = [
  {
    id: 'university-2020',
    degree: 'Bachelor of Computer Science',
    institution: 'Universitas Indonesia',
    period: '2018 - 2022',
    location: 'Jakarta, Indonesia',
    gpa: '3.8/4.0',
    achievements: [
      'Graduated Magna Cum Laude',
      'President of Programming Club',
      'Winner of National Programming Contest'
    ]
  }
];

// Certifications
export const certifications = [
  {
    id: 'aws-cloud',
    name: 'AWS Cloud Practitioner',
    issuer: 'Amazon Web Services',
    date: '2023',
    credentialId: 'AWS-CP-001',
    url: 'https://aws.amazon.com/certification/'
  },
  {
    id: 'react-advanced',
    name: 'Advanced React Development',
    issuer: 'Meta',
    date: '2023',
    credentialId: 'META-ADV-REACT-001',
    url: 'https://developers.facebook.com/certification/'
  },
  {
    id: 'node-certification',
    name: 'Node.js Application Development',
    issuer: 'Linux Foundation',
    date: '2022',
    credentialId: 'LF-NODE-001',
    url: 'https://training.linuxfoundation.org/'
  }
];