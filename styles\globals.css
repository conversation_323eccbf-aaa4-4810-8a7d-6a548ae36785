:root {
  --font-size: 14px;
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(0.98 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(0.98 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.145 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.9 0 0);
  --secondary-foreground: oklch(0.145 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.4 0 0);
  --accent: oklch(0.9 0 0);
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.62 0.18 15);
  --destructive-foreground: oklch(0.98 0 0);
  --border: oklch(0.89 0 0);
  --input: oklch(0.89 0 0);
  --ring: oklch(0.145 0 0);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --radius: 0.625rem;
  --sidebar: oklch(0.95 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.9 0 0);
  --sidebar-accent-foreground: oklch(0.145 0 0);
  --sidebar-border: oklch(0.89 0 0);
  --sidebar-ring: oklch(0.145 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
  scroll-behavior: smooth;
}

body {
  scroll-behavior: smooth;
}

/* Snap scroll container */
.snap-container {
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
  overflow-y: scroll;
  height: 100vh;
}

.snap-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  height: 100vh;
  width: 100vw;
}

/* Enhanced snap scrolling */
@supports (scroll-snap-type: y mandatory) {
  .snap-y {
    scroll-snap-type: y mandatory;
  }
  
  .snap-start {
    scroll-snap-align: start;
    scroll-snap-stop: always;
  }
  
  .snap-mandatory {
    scroll-snap-type: y mandatory;
  }
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Hide scrollbar for snap scroll container */
.snap-container::-webkit-scrollbar {
  display: none;
}

.snap-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Fullscreen hero specific styles */
#home {
  position: relative;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}

/* About section specific styles */
#about {
  position: relative;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}

/* Projects section specific styles */
#projects {
  position: relative;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}

/* Contact section specific styles */
#contact {
  position: relative;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}

/* Navigation z-index above sections */
nav {
  z-index: 50;
}

/* Terminal background effects */
.terminal-bg {
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(31, 41, 55, 0.98) 50%, 
    rgba(17, 24, 39, 0.95) 100%
  );
  backdrop-filter: blur(10px);
}

/* Glow effects for terminal elements */
.glow-green {
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

.glow-blue {
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.glow-purple {
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
}

/* Smooth scroll for manual navigation */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  #home,
  #about,
  #projects,
  #contact {
    padding-left: 0;
  }
}

/* Focus styles for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .snap-container {
    scroll-behavior: auto;
  }
  
  html,
  body {
    scroll-behavior: auto;
  }
}