import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  Palette, 
  Settings, 
  Wrench, 
  Target, 
  Grid,
  List,
  Filter,
  Star,
  TrendingUp,
  Terminal,
  Code,
  Database,
  Cloud,
  Cpu,
  Layers
} from 'lucide-react';

const skillCategories = [
  {
    title: 'Frontend Development',
    icon: Palette,
    color: 'from-blue-500 to-cyan-500',
    iconColor: 'text-blue-400',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/30',
    command: 'npm run dev',
    skills: [
      { name: 'React/Next.js', level: 95, color: 'from-blue-500 to-cyan-500', experience: '4 years', icon: '⚛️' },
      { name: 'TypeScript', level: 90, color: 'from-blue-600 to-blue-800', experience: '3 years', icon: '🔷' },
      { name: 'Tailwind CSS', level: 92, color: 'from-teal-500 to-emerald-500', experience: '3 years', icon: '🎨' },
      { name: 'Vue.js', level: 85, color: 'from-green-500 to-green-600', experience: '2 years', icon: '💚' },
    ],
  },
  {
    title: 'Backend Development',
    icon: Settings,
    color: 'from-green-500 to-emerald-500',
    iconColor: 'text-green-400',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/30',
    command: 'node server.js',
    skills: [
      { name: 'Node.js', level: 88, color: 'from-green-600 to-green-700', experience: '4 years', icon: '🟢' },
      { name: 'Python/Django', level: 85, color: 'from-yellow-500 to-orange-500', experience: '3 years', icon: '🐍' },
      { name: 'PostgreSQL', level: 90, color: 'from-blue-700 to-indigo-600', experience: '4 years', icon: '🐘' },
      { name: 'MongoDB', level: 82, color: 'from-green-700 to-green-800', experience: '2 years', icon: '🍃' },
    ],
  },
  {
    title: 'DevOps & Tools',
    icon: Wrench,
    color: 'from-purple-500 to-indigo-500',
    iconColor: 'text-purple-400',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/30',
    command: 'docker-compose up',
    skills: [
      { name: 'Docker', level: 85, color: 'from-blue-500 to-blue-600', experience: '2 years', icon: '🐳' },
      { name: 'AWS/Cloud', level: 80, color: 'from-orange-500 to-red-500', experience: '2 years', icon: '☁️' },
      { name: 'Git/GitHub', level: 95, color: 'from-gray-600 to-gray-800', experience: '4 years', icon: '🐙' },
      { name: 'CI/CD', level: 78, color: 'from-purple-500 to-purple-700', experience: '1 year', icon: '🔄' },
    ],
  },
  {
    title: 'Design & UX',
    icon: Target,
    color: 'from-pink-500 to-rose-500',
    iconColor: 'text-pink-400',
    bgColor: 'bg-pink-500/10',
    borderColor: 'border-pink-500/30',
    command: 'figma --open',
    skills: [
      { name: 'Figma', level: 88, color: 'from-purple-500 to-pink-500', experience: '3 years', icon: '🎯' },
      { name: 'UI/UX Design', level: 85, color: 'from-pink-500 to-rose-500', experience: '3 years', icon: '✨' },
      { name: 'Responsive Design', level: 92, color: 'from-indigo-500 to-purple-500', experience: '4 years', icon: '📱' },
      { name: 'Accessibility', level: 80, color: 'from-emerald-500 to-teal-500', experience: '2 years', icon: '♿' },
    ],
  },
];

const allSkills = skillCategories.flatMap(category => 
  category.skills.map(skill => ({ ...skill, category: category.title }))
);

const tools = [
  { name: 'VS Code', category: 'Editor', icon: '💻' },
  { name: 'Postman', category: 'API', icon: '📮' },
  { name: 'Figma', category: 'Design', icon: '🎨' },
  { name: 'Slack', category: 'Communication', icon: '💬' },
  { name: 'Jira', category: 'Project Management', icon: '📋' },
  { name: 'GitHub', category: 'Version Control', icon: '🐙' },
  { name: 'Docker', category: 'DevOps', icon: '🐳' },
  { name: 'AWS', category: 'Cloud', icon: '☁️' },
  { name: 'Vercel', category: 'Deployment', icon: '▲' },
  { name: 'Netlify', category: 'Deployment', icon: '🌐' },
  { name: 'MongoDB Atlas', category: 'Database', icon: '🍃' },
  { name: 'Firebase', category: 'Backend', icon: '🔥' },
  { name: 'Supabase', category: 'Backend', icon: '⚡' },
  { name: 'Prisma', category: 'Database', icon: '🔺' }
];

export default function SkillsSection() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'category' | 'list'>('category');

  const categories = ['All', ...skillCategories.map(cat => cat.title)];
  
  const filteredSkills = selectedCategory === 'All' 
    ? allSkills 
    : allSkills.filter(skill => skill.category === selectedCategory);

  return (
    <section id="skills" className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white relative overflow-hidden">
      {/* Terminal Grid Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-4 bg-blue-500/20 text-blue-400 border-blue-500/50 hover:bg-blue-500/30 font-mono">
              <Code className="w-3 h-3 mr-2" />
              $ ls -la skills/
            </Badge>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl lg:text-6xl font-mono mb-6"
          >
            <span className="text-gray-500">#</span>{' '}
            <span className="text-white">Full-Stack</span>
            <br />
            <span className="text-blue-400">Capabilities</span>
            <motion.span
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-blue-400"
            >
              _
            </motion.span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto font-mono"
          >
            <span className="text-gray-500">//</span> Comprehensive skill set covering modern web development,
            <br />
            <span className="text-gray-500">//</span> from frontend frameworks to backend systems and{' '}
            <span className="text-purple-400">cloud deployment</span>.
          </motion.p>
        </motion.div>

        {/* Terminal Filter Controls */}
        <div className="bg-gray-900 border border-gray-700 rounded-lg mb-8 overflow-hidden">
          {/* Terminal Header */}
          <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
            <div className="flex gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full" />
              <div className="w-3 h-3 bg-yellow-500 rounded-full" />
              <div className="w-3 h-3 bg-green-500 rounded-full" />
            </div>
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-mono text-gray-300">skills-filter.sh</span>
            </div>
          </div>

          {/* Filter Content */}
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
              <div className="flex flex-wrap gap-2 items-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-2 mr-4"
                >
                  <Filter className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-mono text-green-400">$ filter --category=</span>
                </motion.div>
                
                {categories.map((category, index) => (
                  <motion.div
                    key={category}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Badge 
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 cursor-pointer transition-all font-mono ${
                        selectedCategory === category 
                          ? 'bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30' 
                          : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                      }`}
                    >
                      {category}
                    </Badge>
                  </motion.div>
                ))}
              </div>

              <div className="flex gap-2">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    onClick={() => setViewMode('category')}
                    className={`text-xs flex items-center gap-1 font-mono ${
                      viewMode === 'category' 
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/50 hover:bg-blue-500/30' 
                        : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'
                    }`}
                  >
                    <Grid className="w-3 h-3" />
                    Grid
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    onClick={() => setViewMode('list')}
                    className={`text-xs flex items-center gap-1 font-mono ${
                      viewMode === 'list' 
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/50 hover:bg-blue-500/30' 
                        : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'
                    }`}
                  >
                    <List className="w-3 h-3" />
                    List
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {viewMode === 'category' ? (
            <motion.div
              key="category-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
            >
              {skillCategories
                .filter(category => selectedCategory === 'All' || category.title === selectedCategory)
                .map((category, categoryIndex) => {
                  const IconComponent = category.icon;
                  return (
                    <motion.div
                      key={category.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
                      layout
                      whileHover={{ scale: 1.02, y: -2 }}
                      className="bg-gray-900 border border-gray-700 rounded-lg p-6 hover:border-green-500/30 transition-all group"
                    >
                      {/* Category Header */}
                      <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                          className={`p-3 rounded-lg ${category.bgColor} border ${category.borderColor} group-hover:scale-110 transition-transform`}
                          whileHover={{ rotate: [0, -5, 5, 0] }}
                          transition={{ duration: 0.5 }}
                        >
                          <IconComponent className={`w-5 h-5 ${category.iconColor}`} />
                        </motion.div>
                        <div className="flex-1">
                          <h3 className="font-mono text-white group-hover:text-green-400 transition-colors">
                            {category.title}
                          </h3>
                          <div className="text-xs text-gray-500 font-mono">
                            $ {category.command}
                          </div>
                        </div>
                      </div>

                      {/* Skills List */}
                      <div className="space-y-4">
                        {category.skills.map((skill, skillIndex) => (
                          <div 
                            key={skill.name} 
                            className="space-y-2"
                            onMouseEnter={() => setHoveredSkill(skill.name)}
                            onMouseLeave={() => setHoveredSkill(null)}
                          >
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-mono text-gray-300 flex items-center gap-2">
                                <span>{skill.icon}</span>
                                {skill.name}
                              </span>
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span className="text-xs text-gray-400 font-mono">{skill.level}%</span>
                              </div>
                            </div>
                            
                            <motion.div
                              initial={{ width: 0 }}
                              whileInView={{ width: '100%' }}
                              transition={{ duration: 1, delay: categoryIndex * 0.1 + skillIndex * 0.1 }}
                              viewport={{ once: true }}
                              className="h-2 bg-gray-800 rounded-full overflow-hidden relative border border-gray-700"
                            >
                              <motion.div
                                initial={{ width: 0 }}
                                whileInView={{ width: `${skill.level}%` }}
                                transition={{ duration: 1.5, delay: categoryIndex * 0.1 + skillIndex * 0.1 }}
                                viewport={{ once: true }}
                                whileHover={{ brightness: 1.2 }}
                                className={`h-full bg-gradient-to-r ${skill.color} rounded-full relative`}
                              >
                                <motion.div
                                  animate={{ x: ['-100%', '100%'] }}
                                  transition={{ 
                                    duration: 2, 
                                    repeat: Infinity, 
                                    ease: 'linear',
                                    repeatDelay: 3 
                                  }}
                                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                                />
                              </motion.div>
                            </motion.div>
                            
                            <AnimatePresence>
                              {hoveredSkill === skill.name && (
                                <motion.div
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  exit={{ opacity: 0, y: -10 }}
                                  className="text-xs text-gray-400 font-mono flex items-center gap-1"
                                >
                                  <TrendingUp className="w-3 h-3 text-green-400" />
                                  Experience: {skill.experience}
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  );
                })}
            </motion.div>
          ) : (
            <motion.div
              key="list-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 gap-4 mb-16"
            >
              {filteredSkills.map((skill, index) => (
                <motion.div
                  key={skill.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  layout
                  whileHover={{ scale: 1.02, y: -2 }}
                  className="bg-gray-900 border border-gray-700 rounded-lg p-4 hover:border-green-500/30 transition-all"
                >
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-mono text-white flex items-center gap-2">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      {skill.name}
                    </h4>
                    <Badge className="bg-gray-800 text-gray-300 border-gray-600 font-mono text-xs">
                      {skill.category}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex-1 h-2 bg-gray-800 rounded-full overflow-hidden border border-gray-700">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: index * 0.05 }}
                        viewport={{ once: true }}
                        className={`h-full bg-gradient-to-r ${skill.color} rounded-full`}
                      />
                    </div>
                    <span className="text-sm font-mono text-gray-400 min-w-[3rem]">
                      {skill.level}%
                    </span>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Tools Section with Terminal Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden"
        >
          {/* Terminal Header */}
          <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
            <div className="flex gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full" />
              <div className="w-3 h-3 bg-yellow-500 rounded-full" />
              <div className="w-3 h-3 bg-green-500 rounded-full" />
            </div>
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-mono text-gray-300">tools-ecosystem.md</span>
            </div>
          </div>

          <div className="p-6">
            <h3 className="text-lg font-mono text-purple-400 mb-6 flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
              >
                <Settings className="w-5 h-5" />
              </motion.div>
              $ npm list --global
            </h3>
            
            <div className="flex flex-wrap gap-3">
              {tools.map((tool, index) => (
                <motion.div
                  key={tool.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.03 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  className="cursor-pointer group"
                >
                  <Badge className="px-3 py-2 font-mono bg-gray-800 text-gray-300 border-gray-600 hover:bg-purple-500/20 hover:text-purple-400 hover:border-purple-500/50 transition-all flex items-center gap-2">
                    <span>{tool.icon}</span>
                    <span className="group-hover:mr-1 transition-all">{tool.name}</span>
                    <motion.span
                      initial={{ opacity: 0, width: 0 }}
                      whileHover={{ opacity: 1, width: 'auto' }}
                      className="text-xs opacity-70 overflow-hidden border-l border-gray-600 pl-2"
                    >
                      {tool.category}
                    </motion.span>
                  </Badge>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}