import { useState, useEffect, useRef } from 'react';
import { APP_CONFIG } from '../config';

interface UseTypingEffectOptions {
  text: string;
  speed?: number;
  delay?: number;
  showCursor?: boolean;
  loop?: boolean;
  onComplete?: () => void;
}

export function useTypingEffect(options: UseTypingEffectOptions) {
  const {
    text,
    speed = APP_CONFIG.animations.durations.typing,
    delay = 0,
    showCursor = true,
    loop = false,
    onComplete
  } = options;

  const [displayedText, setDisplayedText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [cursor, setCursor] = useState(true);
  
  const timeoutRef = useRef<NodeJS.Timeout>();
  const cursorIntervalRef = useRef<NodeJS.Timeout>();

  // Cursor blinking effect
  useEffect(() => {
    if (showCursor) {
      cursorIntervalRef.current = setInterval(() => {
        setCursor(prev => !prev);
      }, 500);
    }

    return () => {
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
      }
    };
  }, [showCursor]);

  // Typing effect
  useEffect(() => {
    const startTyping = () => {
      setIsTyping(true);
      
      const typeNextChar = () => {
        setCurrentIndex(prev => {
          const nextIndex = prev + 1;
          
          if (nextIndex <= text.length) {
            setDisplayedText(text.slice(0, nextIndex));
            
            if (nextIndex === text.length) {
              setIsTyping(false);
              onComplete?.();
              
              if (loop) {
                timeoutRef.current = setTimeout(() => {
                  setCurrentIndex(0);
                  setDisplayedText('');
                }, 2000);
              }
            } else {
              timeoutRef.current = setTimeout(typeNextChar, speed);
            }
          }
          
          return nextIndex;
        });
      };

      timeoutRef.current = setTimeout(typeNextChar, delay);
    };

    startTyping();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, speed, delay, loop, onComplete]);

  const reset = () => {
    setCurrentIndex(0);
    setDisplayedText('');
    setIsTyping(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const start = () => {
    reset();
    setIsTyping(true);
  };

  return {
    displayedText,
    isTyping,
    cursor: showCursor ? cursor : false,
    isComplete: currentIndex === text.length,
    reset,
    start,
  };
}