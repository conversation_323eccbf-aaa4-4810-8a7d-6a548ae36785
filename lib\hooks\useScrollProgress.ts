import { useState, useEffect } from 'react';
import { performanceUtils } from '../utils';

interface UseScrollProgressOptions {
  elementId?: string;
  threshold?: number;
  throttle?: number;
}

export function useScrollProgress(options: UseScrollProgressOptions = {}) {
  const { elementId, threshold = 0.1, throttle = 16 } = options;
  
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [direction, setDirection] = useState<'up' | 'down'>('down');

  useEffect(() => {
    let lastScrollY = 0;

    const updateScrollProgress = performanceUtils.throttle(() => {
      const currentScrollY = window.scrollY;
      
      // Update direction
      setDirection(currentScrollY > lastScrollY ? 'down' : 'up');
      lastScrollY = currentScrollY;

      if (elementId) {
        // Calculate progress for specific element
        const element = document.getElementById(elementId);
        if (element) {
          const rect = element.getBoundingClientRect();
          const windowHeight = window.innerHeight;
          
          // Check if element is in viewport
          const elementVisible = rect.top < windowHeight && rect.bottom > 0;
          setIsVisible(elementVisible);

          if (elementVisible) {
            // Calculate progress from 0 to 1 as element scrolls through viewport
            const progress = Math.max(0, Math.min(1, 
              (windowHeight - rect.top) / (windowHeight + rect.height)
            ));
            setScrollProgress(progress);
          }
        }
      } else {
        // Calculate global scroll progress
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = Math.min(currentScrollY / documentHeight, 1);
        setScrollProgress(progress);
        setIsVisible(progress >= threshold);
      }
    }, throttle);

    window.addEventListener('scroll', updateScrollProgress, { passive: true });
    updateScrollProgress(); // Initial call

    return () => window.removeEventListener('scroll', updateScrollProgress);
  }, [elementId, threshold, throttle]);

  return {
    scrollProgress,
    isVisible,
    direction,
    percentage: Math.round(scrollProgress * 100),
  };
}