import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../lib/utils';
import { useTypingEffect } from '../../lib/hooks/useTypingEffect';
import { TerminalCommand } from '../../lib/types';
import { Terminal, Minimize2, Maximize2, X } from 'lucide-react';

interface TerminalWindowProps {
  commands: TerminalCommand[];
  title?: string;
  autoPlay?: boolean;
  loop?: boolean;
  speed?: number;
  className?: string;
}

export function TerminalWindow({
  commands,
  title = 'Terminal',
  autoPlay = true,
  loop = true,
  speed = 50,
  className
}: TerminalWindowProps) {
  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);
  const [showOutput, setShowOutput] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const currentCommand = commands[currentCommandIndex];
  const fullText = currentCommand ? `$ ${currentCommand.command}\n${currentCommand.output}` : '';

  const {
    displayedText,
    isTyping,
    cursor,
    isComplete
  } = useTypingEffect({
    text: fullText,
    speed,
    showCursor: true,
    onComplete: () => {
      setTimeout(() => {
        if (loop && commands.length > 1) {
          setCurrentCommandIndex(prev => (prev + 1) % commands.length);
        }
      }, 2000);
    }
  });

  return (
    <motion.div
      className={cn(
        'bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ 
        opacity: 1, 
        y: 0,
        height: isMinimized ? 'auto' : 'auto'
      }}
      transition={{ duration: 0.5 }}
    >
      {/* Terminal Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Traffic lights */}
          <div className="flex gap-2">
            <motion.button
              whileHover={{ scale: 1.2 }}
              className="w-3 h-3 bg-red-500 rounded-full"
              onClick={() => {}} // Could add close functionality
            />
            <motion.button
              whileHover={{ scale: 1.2 }}
              className="w-3 h-3 bg-yellow-500 rounded-full"
              onClick={() => setIsMinimized(!isMinimized)}
            />
            <motion.button
              whileHover={{ scale: 1.2 }}
              className="w-3 h-3 bg-green-500 rounded-full"
            />
          </div>

          {/* Terminal info */}
          <div className="flex items-center gap-2">
            <Terminal className="w-4 h-4 text-gray-400" />
            <span className="text-sm font-mono text-gray-300">
              {title}
            </span>
          </div>
        </div>

        {/* Window controls */}
        <div className="flex items-center gap-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="text-gray-400 hover:text-white transition-colors"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
          </motion.button>
        </div>
      </div>

      {/* Terminal Content */}
      <AnimatePresence>
        {!isMinimized && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-6 h-80 overflow-y-auto font-mono text-sm">
              <div className="space-y-4">
                {/* Welcome message */}
                <div className="text-green-400">
                  Welcome to Vicky's Development Environment
                </div>
                <div className="text-blue-400">
                  Full-Stack Developer | React & Node.js Specialist
                </div>
                <div className="text-gray-400">
                  Type 'help' for available commands
                </div>
                <div className="h-px bg-gray-700 my-4" />
                
                {/* Animated command output */}
                <motion.div
                  key={currentCommandIndex}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="space-y-2"
                >
                  <pre className="text-gray-300 whitespace-pre-wrap">
                    {displayedText}
                    <motion.span
                      animate={{ opacity: cursor ? 1 : 0 }}
                      transition={{ duration: 0.1 }}
                      className="text-green-400"
                    >
                      |
                    </motion.span>
                  </pre>
                </motion.div>

                {/* Status indicator */}
                <motion.div
                  className="flex items-center gap-2 text-xs text-gray-500 mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                >
                  <motion.div
                    className={cn(
                      'w-2 h-2 rounded-full',
                      isTyping ? 'bg-yellow-400' : 'bg-green-400'
                    )}
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                  <span>
                    {isTyping ? 'Executing...' : 'Ready'}
                  </span>
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

// Preset terminal configurations
export const HeroTerminal = (props: Partial<TerminalWindowProps>) => (
  <TerminalWindow
    title="vicky@portfolio:~$"
    autoPlay
    loop
    speed={30}
    {...props}
  />
);

export const DemoTerminal = (props: Partial<TerminalWindowProps>) => (
  <TerminalWindow
    title="demo"
    autoPlay={false}
    loop={false}
    speed={20}
    {...props}
  />
);