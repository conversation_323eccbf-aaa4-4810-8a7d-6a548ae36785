import { useState, useEffect, useRef } from 'react';
import { performanceUtils } from '../utils';

interface PerformanceMetrics {
  fps: number;
  memory: number;
  loadTime: number;
  renderTime: number;
  performanceTier: 'low' | 'medium' | 'high';
}

export function usePerformance() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memory: 0,
    loadTime: 0,
    renderTime: 0,
    performanceTier: 'medium',
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(Date.now());
  const renderStartRef = useRef(performance.now());

  useEffect(() => {
    // Measure initial load time
    const loadTime = performance.now();
    
    // Get performance tier
    const performanceTier = performanceUtils.getPerformanceTier();

    let animationFrameId: number;

    const measurePerformance = () => {
      const now = Date.now();
      frameCountRef.current++;

      // Calculate FPS every second
      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));
        
        // Get memory usage if available
        const memory = (performance as any).memory 
          ? Math.round((performance as any).memory.usedJSHeapSize / 1048576) 
          : 0;

        // Calculate render time
        const renderTime = performance.now() - renderStartRef.current;

        setMetrics(prev => ({
          ...prev,
          fps,
          memory,
          loadTime,
          renderTime,
          performanceTier,
        }));

        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }

      animationFrameId = requestAnimationFrame(measurePerformance);
    };

    measurePerformance();

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, []);

  // Performance-based recommendations
  const recommendations = {
    enableParticles: metrics.fps > 30 && metrics.performanceTier !== 'low',
    enableComplexAnimations: metrics.fps > 45 && metrics.performanceTier === 'high',
    enableBlur: metrics.fps > 40,
    particleCount: metrics.performanceTier === 'high' ? 50 : metrics.performanceTier === 'medium' ? 30 : 15,
    animationDuration: metrics.fps < 30 ? 'slow' : 'normal',
  };

  return {
    metrics,
    recommendations,
    isGoodPerformance: metrics.fps > 45,
    isLowPerformance: metrics.fps < 25,
  };
}