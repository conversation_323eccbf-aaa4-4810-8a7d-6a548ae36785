import { forwardRef } from 'react';
import { motion } from 'motion/react';
import { cn } from '../../lib/utils';
import { SectionProps } from '../../lib/types';

interface SectionComponentProps extends SectionProps {
  variant?: 'default' | 'hero' | 'alternate';
  fullHeight?: boolean;
  centered?: boolean;
  animated?: boolean;
  background?: 'transparent' | 'default' | 'subtle' | 'strong';
}

const sectionVariants = {
  default: 'py-20 lg:py-24',
  hero: 'min-h-screen py-20',
  alternate: 'py-16 lg:py-20 bg-muted/30',
};

const backgroundVariants = {
  transparent: '',
  default: 'bg-background',
  subtle: 'bg-muted/30',
  strong: 'bg-card',
};

export const Section = forwardRef<HTMLElement, SectionComponentProps>(
  ({
    id,
    children,
    className,
    variant = 'default',
    fullHeight = false,
    centered = false,
    animated = true,
    background = 'transparent',
    isActive = false,
    ...props
  }, ref) => {
    const MotionSection = animated ? motion.section : 'section';

    const animationProps = animated ? {
      initial: { opacity: 0, y: 20 },
      whileInView: { opacity: 1, y: 0 },
      viewport: { once: true, margin: '-100px' },
      transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] },
    } : {};

    return (
      <MotionSection
        ref={ref}
        id={id}
        className={cn(
          'relative overflow-hidden',
          sectionVariants[variant],
          backgroundVariants[background],
          fullHeight && 'min-h-screen',
          centered && 'flex items-center justify-center',
          className
        )}
        {...animationProps}
        {...props}
      >
        {/* Active section indicator */}
        {isActive && animated && (
          <motion.div
            className="absolute left-0 top-1/2 w-1 h-16 bg-primary rounded-r-full"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          />
        )}

        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </MotionSection>
    );
  }
);

Section.displayName = 'Section';

// Pre-configured section variants
export const HeroSection = forwardRef<HTMLElement, SectionComponentProps>(
  (props, ref) => (
    <Section ref={ref} variant="hero" fullHeight centered {...props} />
  )
);

export const ContentSection = forwardRef<HTMLElement, SectionComponentProps>(
  (props, ref) => (
    <Section ref={ref} variant="default" {...props} />
  )
);

export const AlternateSection = forwardRef<HTMLElement, SectionComponentProps>(
  (props, ref) => (
    <Section ref={ref} variant="alternate" background="subtle" {...props} />
  )
);

HeroSection.displayName = 'HeroSection';
ContentSection.displayName = 'ContentSection';
AlternateSection.displayName = 'AlternateSection';