import { useState } from 'react';
import { AnimatePresence } from 'motion/react';
import { Toaster } from '../ui/sonner';
import { usePerformance } from '../../lib/hooks/usePerformance';

interface PageLayoutProps {
  children: React.ReactNode;
  showSplash?: boolean;
  onSplashComplete?: () => void;
}

export function PageLayout({ 
  children, 
  showSplash = false, 
  onSplashComplete 
}: PageLayoutProps) {
  const [isLoading, setIsLoading] = useState(showSplash);
  const { metrics, recommendations } = usePerformance();

  const handleSplashComplete = () => {
    setIsLoading(false);
    onSplashComplete?.();
  };

  return (
    <div className="min-h-screen bg-background relative">
      <AnimatePresence mode="wait">
        {isLoading ? (
          <div key="splash">
            {/* Splash screen would go here */}
            <button onClick={handleSplashComplete}>Complete Splash</button>
          </div>
        ) : (
          <div key="main" className="relative">
            {children}
          </div>
        )}
      </AnimatePresence>
      
      {/* Global Toast Container */}
      <Toaster 
        position="bottom-right"
        toastOptions={{
          style: {
            background: 'hsl(var(--card))',
            border: '1px solid hsl(var(--border))',
            color: 'hsl(var(--card-foreground))',
          },
        }}
      />

      {/* Performance Debug Info (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-50">
          <div>FPS: {metrics.fps}</div>
          <div>Tier: {metrics.performanceTier}</div>
          <div>Particles: {recommendations.particleCount}</div>
        </div>
      )}
    </div>
  );
}

// Context provider for global app state
import { createContext, useContext } from 'react';

interface AppContextType {
  performance: ReturnType<typeof usePerformance>;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const performance = usePerformance();

  return (
    <AppContext.Provider
      value={{
        performance,
        isLoading,
        setIsLoading,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}