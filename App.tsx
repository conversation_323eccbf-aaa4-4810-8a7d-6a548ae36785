import { useState, useEffect } from 'react';
import { AnimatePresence } from 'motion/react';
import { Toaster } from './components/ui/sonner';

// Components
import Navigation from './components/Navigation';
import HeroSection from './components/HeroSection';
import AboutSection from './components/AboutSection';
import ProjectsSection from './components/ProjectsSection';
import ContactSection from './components/ContactSection';
import SplashScreen from './components/SplashScreen';

export default function App() {
  const [showSplash, setShowSplash] = useState(true);
  const [currentSection, setCurrentSection] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  const sections = ['home', 'about', 'projects', 'contact'];

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  // Snap scroll behavior
  useEffect(() => {
    if (showSplash) return;

    let timeout: NodeJS.Timeout;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      
      if (isScrolling) return;

      const direction = e.deltaY > 0 ? 1 : -1;
      const nextSection = currentSection + direction;

      if (nextSection >= 0 && nextSection < sections.length) {
        setIsScrolling(true);
        setCurrentSection(nextSection);
        
        // Scroll to section
        const element = document.getElementById(sections[nextSection]);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }

        // Reset scrolling flag
        timeout = setTimeout(() => {
          setIsScrolling(false);
        }, 1000);
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (isScrolling) return;

      let nextSection = currentSection;

      switch (e.key) {
        case 'ArrowDown':
        case 'PageDown':
          e.preventDefault();
          nextSection = Math.min(currentSection + 1, sections.length - 1);
          break;
        case 'ArrowUp':
        case 'PageUp':
          e.preventDefault();
          nextSection = Math.max(currentSection - 1, 0);
          break;
        case 'Home':
          e.preventDefault();
          nextSection = 0;
          break;
        case 'End':
          e.preventDefault();
          nextSection = sections.length - 1;
          break;
        default:
          return;
      }

      if (nextSection !== currentSection) {
        setIsScrolling(true);
        setCurrentSection(nextSection);
        
        const element = document.getElementById(sections[nextSection]);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }

        timeout = setTimeout(() => {
          setIsScrolling(false);
        }, 1000);
      }
    };

    // Add event listeners
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      if (timeout) clearTimeout(timeout);
    };
  }, [currentSection, isScrolling, showSplash]);

  // Handle navigation clicks
  const handleNavigationClick = (sectionId: string) => {
    const sectionIndex = sections.findIndex(section => section === sectionId);
    if (sectionIndex !== -1 && sectionIndex !== currentSection) {
      setIsScrolling(true);
      setCurrentSection(sectionIndex);
      
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }

      setTimeout(() => {
        setIsScrolling(false);
      }, 1000);
    }
  };

  return (
    <div className="min-h-screen bg-background overflow-hidden">
      <AnimatePresence mode="wait">
        {showSplash ? (
          <SplashScreen key="splash" onComplete={handleSplashComplete} />
        ) : (
          <div key="main" className="relative">
            <Navigation onNavigate={handleNavigationClick} />
            
            {/* Section Container */}
            <div className="snap-y snap-mandatory overflow-y-auto h-screen">
              {/* Hero Section */}
              <div className="snap-start h-screen">
                <HeroSection />
              </div>
              
              {/* About Section */}
              <div className="snap-start h-screen">
                <AboutSection />
              </div>
              
              {/* Projects Section */}
              <div className="snap-start h-screen">
                <ProjectsSection />
              </div>
              
              {/* Contact Section */}
              <div className="snap-start h-screen">
                <ContactSection />
              </div>
            </div>

            {/* Section Indicator */}
            <div className="fixed right-8 top-1/2 -translate-y-1/2 z-50 space-y-3">
              {sections.map((section, index) => (
                <button
                  key={section}
                  onClick={() => handleNavigationClick(section)}
                  className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
                    currentSection === index
                      ? 'bg-green-400 border-green-400 shadow-lg shadow-green-400/50'
                      : 'bg-transparent border-gray-500 hover:border-green-400'
                  }`}
                  aria-label={`Go to ${section} section`}
                />
              ))}
            </div>

            {/* Scroll Hint */}
            {currentSection === 0 && !isScrolling && (
              <div className="fixed bottom-8 left-1/2 -translate-x-1/2 z-40 text-gray-400 font-mono text-sm text-center">
                <div className="space-y-2">
                  <p>Scroll to explore</p>
                  <div className="flex items-center justify-center">
                    <div className="w-6 h-10 border-2 border-gray-500 rounded-full flex justify-center">
                      <div className="w-1 h-3 bg-gray-500 rounded-full mt-2 animate-bounce" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </AnimatePresence>
      
      {/* Global Toast Container */}
      <Toaster 
        position="bottom-right"
        toastOptions={{
          style: {
            background: 'hsl(var(--card))',
            border: '1px solid hsl(var(--border))',
            color: 'hsl(var(--card-foreground))',
          },
        }}
      />
    </div>
  );
}