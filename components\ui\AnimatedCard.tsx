import { forwardRef } from 'react';
import { motion } from 'motion/react';
import { cn, animations } from '../../lib/utils';
import { CardProps } from '../../lib/types';
import { Card } from './card';

interface AnimatedCardProps extends CardProps {
  delay?: number;
  hover?: boolean;
  press?: boolean;
  glow?: boolean;
  float?: boolean;
}

export const AnimatedCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({
    children,
    className,
    variant = 'default',
    delay = 0,
    hover = true,
    press = true,
    glow = false,
    float = false,
    onClick,
    ...props
  }, ref) => {
    const isInteractive = onClick || variant === 'interactive';

    const hoverProps = hover ? {
      whileHover: {
        ...animations.hover.lift,
        ...(glow && { boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)' }),
      }
    } : {};

    const tapProps = press ? {
      whileTap: animations.tap.scale
    } : {};

    const floatProps = float ? {
      animate: {
        y: [0, -5, 0],
      },
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut',
        delay: delay * 0.5,
      }
    } : {};

    return (
      <motion.div
        ref={ref}
        className={cn(
          'group',
          isInteractive && 'cursor-pointer',
          className
        )}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{
          duration: 0.5,
          delay,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
        onClick={onClick}
        {...hoverProps}
        {...tapProps}
        {...floatProps}
        {...props}
      >
        <Card 
          className={cn(
            'transition-all duration-300',
            hover && 'group-hover:shadow-lg',
            glow && 'group-hover:shadow-primary/20',
            variant === 'interactive' && 'border-primary/20 hover:border-primary/40',
            variant === 'highlight' && 'border-primary/50 bg-primary/5'
          )}
        >
          {children}
        </Card>
      </motion.div>
    );
  }
);

AnimatedCard.displayName = 'AnimatedCard';

// Specialized card variants
export const ProjectCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  (props, ref) => (
    <AnimatedCard
      ref={ref}
      variant="interactive"
      hover
      press
      glow
      {...props}
    />
  )
);

export const SkillCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  (props, ref) => (
    <AnimatedCard
      ref={ref}
      hover
      float
      {...props}
    />
  )
);

export const StatCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  (props, ref) => (
    <AnimatedCard
      ref={ref}
      variant="highlight"
      hover
      press
      {...props}
    />
  )
);

ProjectCard.displayName = 'ProjectCard';
SkillCard.displayName = 'SkillCard';
StatCard.displayName = 'StatCard';