import { useState, useEffect, useRef } from 'react';
import { animations, performanceUtils } from '../utils';

interface UseAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  delay?: number;
}

export function useAnimation(options: UseAnimationOptions = {}) {
  const { threshold = 0.1, rootMargin = '0px', triggerOnce = true, delay = 0 } = options;
  
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const visible = entry.isIntersecting;
        
        if (visible && (!triggerOnce || !hasTriggered)) {
          setTimeout(() => {
            setIsVisible(true);
            setHasTriggered(true);
          }, delay);
        } else if (!triggerOnce && !visible) {
          setIsVisible(false);
        }
      },
      { threshold, rootMargin }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [threshold, rootMargin, triggerOnce, delay, hasTriggered]);

  // Pre-built animation variants
  const variants = {
    fadeIn: animations.fadeIn(delay),
    fadeInUp: animations.fadeInUp(delay),
    fadeInDown: animations.fadeInDown(delay),
    fadeInLeft: animations.fadeInLeft(delay),
    fadeInRight: animations.fadeInRight(delay),
    scaleIn: animations.scaleIn(delay),
  };

  return {
    ref: elementRef,
    isVisible,
    hasTriggered,
    variants,
    controls: isVisible,
  };
}