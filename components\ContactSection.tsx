import { useState, useEffect, useRef } from 'react';
import { motion } from 'motion/react';
import { Mail, Phone, MapPin, MessageSquare, Send, Github, Linkedin, Twitter } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { toast } from 'sonner@2.0.3';

const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンVICKY';

// Enhanced Typewriter component with keyword highlighting
const TypewriterText = ({ 
  text, 
  className = '', 
  speed = 40, 
  onComplete,
  startDelay = 0,
  highlightWords = []
}: { 
  text: string; 
  className?: string; 
  speed?: number; 
  onComplete?: () => void;
  startDelay?: number;
  highlightWords?: string[];
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setHasStarted(true);
    }, startDelay);
    
    return () => clearTimeout(startTimer);
  }, [startDelay]);

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete();
      }
      return;
    }

    const char = text[currentIndex];
    let delay = speed;
    
    if (char === '.' || char === '!' || char === '?') delay += 150;
    else if (char === ',' || char === ';') delay += 80;
    else if (char === ' ') delay -= 10;
    
    delay += Math.random() * 30 - 15;

    const timer = setTimeout(() => {
      setDisplayedText(text.slice(0, currentIndex + 1));
      setCurrentIndex(currentIndex + 1);
    }, Math.max(delay, 15));
    
    return () => clearTimeout(timer);
  }, [currentIndex, text, speed, onComplete, hasStarted]);

  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setHasStarted(false);
  }, [text]);

  // Highlight keywords
  const getHighlightedText = (text: string) => {
    if (!highlightWords.length) return text;
    
    let highlightedText = text;
    highlightWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<span class="text-blue-400">$1</span>`);
    });
    
    return highlightedText;
  };

  return (
    <span className={className}>
      <span dangerouslySetInnerHTML={{ __html: getHighlightedText(displayedText) }} />
      {hasStarted && currentIndex < text.length && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 0.8, repeat: Infinity }}
          className="text-green-400"
        >
          |
        </motion.span>
      )}
    </span>
  );
};

// Interactive Contact Form Component
const ContactForm = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast.success('Message sent successfully! I\'ll get back to you soon.');
    setFormData({ name: '', email: '', message: '' });
    setIsSubmitting(false);
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-full flex items-center justify-center perspective-1000"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ perspective: '1000px' }}
    >
      {/* Main Contact Form Container */}
      <motion.div
        className="relative transform-gpu w-full max-w-lg"
        animate={{
          scale: isHovered ? 1.02 : 1
        }}
        transition={{ type: "spring", stiffness: 150, damping: 20 }}
        style={{
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Form Frame */}
        <motion.div
          className="relative bg-gray-900/90 border border-green-400/30 rounded-xl shadow-2xl overflow-hidden backdrop-blur-sm"
          animate={{
            boxShadow: [
              '0 8px 32px rgba(34, 197, 94, 0.3)',
              '0 12px 40px rgba(59, 130, 246, 0.4)',
              '0 8px 32px rgba(34, 197, 94, 0.3)'
            ]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          {/* Form Header */}
          <div className="bg-gray-800/90 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full" />
                <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                <div className="w-3 h-3 bg-green-500 rounded-full" />
              </div>
              <div className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4 text-green-400" />
                <span className="text-sm font-mono text-gray-300">
                  ~/contact/send-message.sh
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full"
              />
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Contact Info Display */}
              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <Mail className="w-5 h-5 text-blue-400" />
                  <div>
                    <div className="text-sm font-mono text-gray-300"><EMAIL></div>
                    <div className="text-xs font-mono text-gray-500">Direct email</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <MapPin className="w-5 h-5 text-purple-400" />
                  <div>
                    <div className="text-sm font-mono text-gray-300">San Francisco, CA</div>
                    <div className="text-xs font-mono text-gray-500">Available remotely</div>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex gap-3 justify-center pt-2">
                  {[
                    { icon: Github, color: 'text-gray-400', url: '#' },
                    { icon: Linkedin, color: 'text-blue-400', url: '#' },
                    { icon: Twitter, color: 'text-cyan-400', url: '#' }
                  ].map((social, index) => {
                    const IconComponent = social.icon;
                    return (
                      <motion.a
                        key={index}
                        href={social.url}
                        className={`p-2 rounded-lg bg-gray-800 border border-gray-600 hover:border-green-400/50 transition-colors ${social.color}`}
                        whileHover={{ scale: 1.1, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <IconComponent className="w-4 h-4" />
                      </motion.a>
                    );
                  })}
                </div>
              </div>

              {/* Form Fields */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-mono text-gray-400 mb-2">
                    $ echo "name" &gt;&gt; contact.json
                  </label>
                  <motion.div whileFocus={{ scale: 1.02 }}>
                    <Input
                      value={formData.name}
                      onChange={(e) => handleChange('name', e.target.value)}
                      placeholder="Your name"
                      className="bg-gray-800 border-gray-600 text-white font-mono focus:border-green-400 transition-colors"
                      required
                    />
                  </motion.div>
                </div>

                <div>
                  <label className="block text-sm font-mono text-gray-400 mb-2">
                    $ echo "email" &gt;&gt; contact.json
                  </label>
                  <motion.div whileFocus={{ scale: 1.02 }}>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="bg-gray-800 border-gray-600 text-white font-mono focus:border-green-400 transition-colors"
                      required
                    />
                  </motion.div>
                </div>

                <div>
                  <label className="block text-sm font-mono text-gray-400 mb-2">
                    $ cat &gt; message.txt
                  </label>
                  <motion.div whileFocus={{ scale: 1.02 }}>
                    <Textarea
                      value={formData.message}
                      onChange={(e) => handleChange('message', e.target.value)}
                      placeholder="Tell me about your project..."
                      className="bg-gray-800 border-gray-600 text-white font-mono focus:border-green-400 transition-colors min-h-[100px]"
                      required
                    />
                  </motion.div>
                </div>
              </div>

              {/* Submit Button */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-green-500/20 border border-green-500/50 text-green-400 hover:bg-green-500/30 font-mono py-3"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full"
                      />
                      $ sending...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Send className="w-4 h-4" />
                      $ ./send-message.sh
                    </div>
                  )}
                </Button>
              </motion.div>
            </form>
          </div>
        </motion.div>
      </motion.div>

      {/* Background Matrix Rain */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <div className="absolute inset-0 grid grid-cols-8 gap-1">
          {Array.from({ length: 30 }).map((_, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, 500],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "linear"
              }}
              className="text-green-400 font-mono text-xs"
            >
              {matrixChars[Math.floor(Math.random() * matrixChars.length)]}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced Ambient Lighting Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-48 h-48 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(34, 197, 94, 0.15) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-56 h-56 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.12) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1.2, 0.8, 1.2],
            opacity: [0.2, 0.6, 0.2]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
      </div>

      {/* Terminal Grid Overlay */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(rgba(6, 182, 212, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(6, 182, 212, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px, 60px 60px, 20px 20px, 20px 20px'
          }}
        />
      </div>
    </div>
  );
};

export default function ContactSection() {
  const [currentContactIndex, setContactIndex] = useState(0);

  const contactCommands = [
    {
      command: "$ ping <EMAIL>",
      output: "Ready to collaborate on your next project! Let's discuss how we can turn your ideas into reality.",
      keywords: ["collaborate", "project", "ideas"]
    },
    {
      command: "$ cat /usr/vicky/availability.txt",
      output: "Currently available for freelance projects and full-time opportunities. Remote work preferred with flexible hours.",
      keywords: ["available", "freelance", "remote"]
    },
    {
      command: "$ grep -i 'communication' ~/work-style.md",
      output: "I believe in clear communication, regular updates, and collaborative development. Your vision is my priority.",
      keywords: ["communication", "collaborative", "vision"]
    },
    {
      command: "$ ./response-time --average",
      output: "Average response time: &lt; 24 hours. I value your time and ensure prompt replies to all inquiries.",
      keywords: ["24 hours", "prompt", "inquiries"]
    }
  ];

  const handleContactComplete = () => {
    if (currentContactIndex < contactCommands.length - 1) {
      setTimeout(() => {
        setContactIndex(prev => prev + 1);
      }, 2000);
    }
  };

  return (
    <section
      id="contact"
      className="h-screen w-screen relative overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black text-white"
    >
      {/* Futuristic Grid Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px)
          `,
          backgroundSize: '100px 100px, 100px 100px, 20px 20px, 20px 20px'
        }} />
      </div>

      {/* Main Content Container */}
      <div className="relative h-full w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-12 gap-8 h-full items-center">
          
          {/* Left Column - Contact Commands */}
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="lg:col-span-5 space-y-6 font-mono"
          >
            {/* Section Title */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                >
                  <MessageSquare className="w-8 h-8 text-green-400" />
                </motion.div>
                <h2 className="text-3xl lg:text-4xl font-bold text-green-400">
                  <TypewriterText 
                    text="$ ./get-in-touch"
                    speed={100}
                    startDelay={800}
                  />
                </h2>
              </div>
              
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 2, duration: 1 }}
                className="h-px bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 max-w-md origin-left"
              />
            </motion.div>

            {/* Contact Commands Display */}
            <div className="space-y-8">
              {/* Display completed contact commands */}
              {contactCommands.slice(0, currentContactIndex).map((contact, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    {contact.command}
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <span dangerouslySetInnerHTML={{
                      __html: contact.output.replace(
                        new RegExp(`\\b(${contact.keywords.join('|')})\\b`, 'gi'),
                        '<span class="text-blue-400 glow-blue">$1</span>'
                      )
                    }} />
                  </div>
                </motion.div>
              ))}
              
              {/* Current typing contact command */}
              {currentContactIndex < contactCommands.length && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    <TypewriterText 
                      text={contactCommands[currentContactIndex].command}
                      speed={40}
                      startDelay={3000}
                    />
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <TypewriterText 
                      text={contactCommands[currentContactIndex].output}
                      speed={30}
                      startDelay={5000}
                      onComplete={handleContactComplete}
                      highlightWords={contactCommands[currentContactIndex].keywords}
                    />
                  </div>
                </motion.div>
              )}
            </div>

            {/* Status Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 4 }}
              className="flex items-center gap-3 text-sm text-gray-400"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-2 h-2 bg-green-400 rounded-full"
              />
              <span>Status: Online | Response: {currentContactIndex + 1}/{contactCommands.length}</span>
            </motion.div>
          </motion.div>

          {/* Center Divider */}
          <motion.div
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ delay: 1, duration: 1.5 }}
            className="hidden lg:block lg:col-span-1 h-full flex items-center justify-center"
          >
            <motion.div
              animate={{ 
                opacity: [0.3, 1, 0.3],
                boxShadow: [
                  '0 0 20px rgba(34, 197, 94, 0.3)',
                  '0 0 40px rgba(59, 130, 246, 0.6)',
                  '0 0 20px rgba(6, 182, 212, 0.4)'
                ]
              }}
              transition={{ duration: 4, repeat: Infinity }}
              className="w-px h-4/5 bg-gradient-to-b from-transparent via-green-400 to-transparent"
            />
          </motion.div>

          {/* Right Column - Interactive Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            className="lg:col-span-6 h-full flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.3, rotateY: 90 }}
              animate={{ scale: 1, rotateY: 0 }}
              transition={{ delay: 1.5, duration: 1.2, ease: "easeOut" }}
              className="relative w-full h-full"
            >
              <ContactForm />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}