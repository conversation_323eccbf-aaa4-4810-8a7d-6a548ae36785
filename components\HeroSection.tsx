import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import ParticleBackground from './ParticleBackground';
import { 
  CheckCircle, 
  ArrowDown, 
  Terminal,
  Code2,
  Database,
  Server,
  Globe,
  Minimize2,
  Maximize2,
  X,
  Coffee,
  GitBranch,
  User,
  MapPin
} from 'lucide-react';
import { APP_CONFIG } from '../lib/config';
import { techStack } from '../lib/data/portfolio';
import { animations } from '../lib/utils';

const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンVICKY';

// Enhanced Typewriter component with more natural typing
const TypewriterText = ({ 
  text, 
  className = '', 
  speed = 80, 
  onComplete,
  startDelay = 0
}: { 
  text: string; 
  className?: string; 
  speed?: number; 
  onComplete?: () => void;
  startDelay?: number;
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setHasStarted(true);
    }, startDelay);
    
    return () => clearTimeout(startTimer);
  }, [startDelay]);

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete();
      }
      return;
    }

    // Natural typing variation
    const char = text[currentIndex];
    let delay = speed;
    
    // Add pauses for punctuation
    if (char === '.' || char === '!' || char === '?') delay += 200;
    else if (char === ',' || char === ';') delay += 100;
    else if (char === ' ') delay -= 20;
    
    // Add slight randomness for natural feel
    delay += Math.random() * 40 - 20;

    const timer = setTimeout(() => {
      setDisplayedText(text.slice(0, currentIndex + 1));
      setCurrentIndex(currentIndex + 1);
    }, Math.max(delay, 20));
    
    return () => clearTimeout(timer);
  }, [currentIndex, text, speed, onComplete, hasStarted]);

  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setHasStarted(false);
  }, [text]);

  return (
    <span className={className}>
      {displayedText}
      {hasStarted && currentIndex < text.length && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 0.6, repeat: Infinity }}
          className="text-green-400"
        >
          |
        </motion.span>
      )}
    </span>
  );
};

export default function HeroSection() {
  const [currentCommand, setCurrentCommand] = useState(0);
  const [displayedText, setDisplayedText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [showCursor, setShowCursor] = useState(true);
  const [matrixRain, setMatrixRain] = useState<string[]>([]);

  // Terminal typing effect for commands
  useEffect(() => {
    if (currentCommand < APP_CONFIG.terminalCommands.length) {
      const command = APP_CONFIG.terminalCommands[currentCommand];
      const fullText = `$ ${command.command}\n${command.output}`;
      
      if (displayedText.length < fullText.length) {
        const timer = setTimeout(() => {
          setDisplayedText(fullText.slice(0, displayedText.length + 1));
        }, Math.random() * 50 + 30);
        return () => clearTimeout(timer);
      } else {
        const timer = setTimeout(() => {
          setCurrentCommand(prev => (prev + 1) % APP_CONFIG.terminalCommands.length);
          setDisplayedText('');
        }, 2000);
        return () => clearTimeout(timer);
      }
    }
  }, [displayedText, currentCommand]);

  // Cursor blinking effect
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);
    return () => clearInterval(interval);
  }, []);

  // Matrix rain effect
  useEffect(() => {
    const generateMatrixRain = () => {
      const newRain = [];
      for (let i = 0; i < 50; i++) {
        newRain.push(matrixChars[Math.floor(Math.random() * matrixChars.length)]);
      }
      setMatrixRain(newRain);
    };

    generateMatrixRain();
    const interval = setInterval(generateMatrixRain, 100);
    return () => clearInterval(interval);
  }, []);

  return (
    <section
      id="home"
      className="h-screen w-screen fixed top-0 left-0 z-0 overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white"
    >
      {/* Matrix Rain Background */}
      <div className="absolute inset-0 overflow-hidden opacity-20">
        <div className="absolute inset-0 grid grid-cols-12 gap-1">
          {matrixRain.map((char, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, window.innerHeight || 1000],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: index * 0.1,
                ease: "linear"
              }}
              className="text-green-400 font-mono text-sm"
            >
              {char}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Particle Background */}
      <div className="absolute inset-0 opacity-30">
        <ParticleBackground />
      </div>

      {/* Grid Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      <div className="relative h-full w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="grid lg:grid-cols-12 gap-8 items-center h-full">
          {/* Left Content - 7 columns */}
          <motion.div
            {...animations.fadeInLeft(0)}
            className="lg:col-span-7 space-y-8"
          >
            {/* Status Badge */}
            <motion.div
              {...animations.fadeInUp(0.2)}
            >
              <Badge className="bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 transition-colors">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="mr-2"
                >
                  <CheckCircle className="w-3 h-3" />
                </motion.div>
                <span className="font-mono">
                  <TypewriterText 
                    text={APP_CONFIG.personal.status}
                    speed={60}
                    startDelay={500}
                  />
                </span>
              </Badge>
            </motion.div>

            {/* Main Title */}
            <div className="space-y-4">
              <motion.h1
                {...animations.fadeInUp(0.3)}
                className="text-4xl md:text-6xl lg:text-7xl font-bold font-mono leading-tight"
              >
                <span className="text-gray-500">$</span>{' '}
                <span className="text-green-400">
                  <TypewriterText 
                    text="whoami"
                    speed={120}
                    startDelay={1000}
                  />
                </span>
                <br />
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2.5 }}
                  className="text-white"
                >
                  <TypewriterText 
                    text="vicky"
                    speed={100}
                    startDelay={2500}
                  />
                </motion.span>
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 3.5 }}
                  className="text-blue-400"
                >
                  <TypewriterText 
                    text="_mosafan"
                    speed={80}
                    startDelay={3500}
                  />
                </motion.span>
                <motion.span
                  animate={{ opacity: showCursor ? 1 : 0 }}
                  className="text-green-400"
                >
                  _
                </motion.span>
              </motion.h1>

              <motion.div
                {...animations.fadeInUp(0.4)}
                className="space-y-2"
              >
                <p className="text-lg md:text-xl text-gray-300 font-mono">
                  <span className="text-gray-500">#</span>{' '}
                  <TypewriterText 
                    text={APP_CONFIG.personal.title}
                    speed={70}
                    startDelay={4500}
                  />
                </p>
                <div className="flex items-center gap-4 text-sm font-mono text-gray-400">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4 text-blue-400" />
                    <TypewriterText 
                      text={APP_CONFIG.personal.location}
                      speed={60}
                      startDelay={6000}
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4 text-green-400" />
                    <TypewriterText 
                      text={`${APP_CONFIG.personal.experience} Experience`}
                      speed={60}
                      startDelay={7000}
                    />
                  </div>
                </div>
                <p className="text-md text-gray-300 font-mono max-w-2xl">
                  <span className="text-gray-500">//</span>{' '}
                  <TypewriterText 
                    text="Crafting scalable web applications from frontend to backend, specializing in React, Node.js, and modern web technologies."
                    speed={45}
                    startDelay={8000}
                  />
                </p>
              </motion.div>
            </div>

            {/* Tech Stack Icons */}
            <motion.div
              {...animations.fadeInUp(0.5)}
              className="flex flex-wrap gap-4"
            >
              {techStack.map((tech, index) => {
                const IconComponent = tech.icon;
                return (
                  <motion.div
                    key={tech.name}
                    {...animations.scaleIn(10 + index * 0.5)}
                    whileHover={animations.hover.lift}
                    className={`${tech.bgColor} border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-all group cursor-pointer`}
                  >
                    <motion.div
                      whileHover={{ rotate: [0, -5, 5, 0] }}
                      transition={{ duration: 0.5 }}
                      className="flex items-center gap-2"
                    >
                      <IconComponent className={`w-5 h-5 ${tech.color}`} />
                      <span className="text-sm font-mono text-gray-300 group-hover:text-white transition-colors">
                        {tech.name}
                      </span>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          </motion.div>

          {/* Right Content - 5 columns */}
          <motion.div
            {...animations.fadeInRight(0.3)}
            className="lg:col-span-5 space-y-8"
          >
            {/* Terminal Window */}
            <motion.div
              {...animations.fadeInUp(0.5)}
              className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden"
            >
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex gap-2">
                    <motion.div
                      whileHover={animations.hover.scale}
                      className="w-3 h-3 bg-red-500 rounded-full cursor-pointer"
                    />
                    <motion.div
                      whileHover={animations.hover.scale}
                      className="w-3 h-3 bg-yellow-500 rounded-full cursor-pointer"
                    />
                    <motion.div
                      whileHover={animations.hover.scale}
                      className="w-3 h-3 bg-green-500 rounded-full cursor-pointer"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Terminal className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-mono text-gray-300">
                      vicky@portfolio:~$
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <motion.button
                    whileHover={animations.hover.scale}
                    whileTap={animations.tap.scale}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <Minimize2 className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    whileHover={animations.hover.scale}
                    whileTap={animations.tap.scale}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>

              {/* Terminal Content */}
              <div className="p-6 h-80 overflow-y-auto font-mono text-sm">
                <div className="space-y-4">
                  <div className="text-green-400">
                    <TypewriterText 
                      text="Welcome to Vicky's Development Environment"
                      speed={50}
                      startDelay={9000}
                    />
                  </div>
                  <div className="text-blue-400">
                    <TypewriterText 
                      text="Full-Stack Developer | React & Node.js Specialist"
                      speed={45}
                      startDelay={11000}
                    />
                  </div>
                  <div className="text-gray-400">
                    <TypewriterText 
                      text="Type 'help' for available commands"
                      speed={40}
                      startDelay={13000}
                    />
                  </div>
                  <div className="h-px bg-gray-700 my-4" />
                  
                  <motion.div
                    key={currentCommand}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="space-y-2"
                  >
                    <pre className="text-gray-300 whitespace-pre-wrap">
                      {displayedText}
                      {showCursor && <span className="text-green-400">|</span>}
                    </pre>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}