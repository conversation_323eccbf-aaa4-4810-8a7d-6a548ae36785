// Global type definitions for the application

import { LucideIcon } from 'lucide-react';

// Base Types
export interface BaseProps {
  className?: string;
  children?: React.ReactNode;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  command: string;
  icon?: string;
}

export interface NavigationProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
}

// Animation Types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  ease?: string | number[];
  repeat?: number;
  repeatType?: 'loop' | 'reverse' | 'mirror';
}

export interface SpringConfig {
  stiffness?: number;
  damping?: number;
  restDelta?: number;
}

export interface MotionProps {
  initial?: Record<string, any>;
  animate?: Record<string, any>;
  exit?: Record<string, any>;
  transition?: AnimationConfig;
  whileHover?: Record<string, any>;
  whileTap?: Record<string, any>;
  whileInView?: Record<string, any>;
}

// Component Props Types
export interface SectionProps extends BaseProps {
  id?: string;
  isActive?: boolean;
}

export interface CardProps extends BaseProps {
  title?: string;
  description?: string;
  icon?: LucideIcon;
  variant?: 'default' | 'interactive' | 'highlight';
  onClick?: () => void;
}

export interface ButtonProps extends BaseProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

// Terminal Types
export interface TerminalCommand {
  command: string;
  output: string;
}

export interface TerminalProps {
  commands?: TerminalCommand[];
  autoPlay?: boolean;
  speed?: number;
  showCursor?: boolean;
}

// Particle System Types
export interface Particle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  life: number;
  maxLife: number;
}

export interface ParticleSystemProps {
  particleCount?: number;
  particleColor?: string;
  particleSize?: [number, number]; // [min, max]
  particleSpeed?: [number, number]; // [min, max]
  particleLife?: [number, number]; // [min, max]
  enableInteraction?: boolean;
}

// Stats/Analytics Types
export interface Stat {
  icon: LucideIcon;
  label: string;
  value: string | number;
  color: string;
  bgColor: string;
  description?: string;
}

export interface StatsGridProps extends BaseProps {
  stats: Stat[];
  columns?: number;
  animated?: boolean;
}

// Skill Types
export interface Skill {
  name: string;
  level: number;
  category: string;
  icon?: LucideIcon;
  color?: string;
  description?: string;
}

export interface SkillCategory {
  name: string;
  skills: Skill[];
  icon: LucideIcon;
  color: string;
}

// Project Types
export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  technologies: string[];
  category: string;
  status: 'completed' | 'in-progress' | 'planned';
  links: {
    live?: string;
    github?: string;
    demo?: string;
  };
  features?: string[];
  challenges?: string[];
  learnings?: string[];
}

export interface ProjectFilterProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormProps {
  onSubmit: (data: ContactFormData) => void;
  loading?: boolean;
}

// Theme Types
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  muted: string;
  border: string;
}

export interface ThemeConfig {
  colors: ThemeColors;
  fonts: {
    mono: string;
    sans: string;
  };
  animations: {
    duration: Record<string, number>;
    easing: Record<string, string>;
  };
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Event Types
export interface ScrollEvent {
  scrollY: number;
  direction: 'up' | 'down';
  progress: number;
}

export interface ResizeEvent {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

// API Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ContactFormResponse extends ApiResponse {
  data?: {
    id: string;
    timestamp: string;
  };
}

// Performance Types
export interface PerformanceMetrics {
  fps: number;
  memory?: number;
  loadTime: number;
  renderTime: number;
}

// Feature Flags
export interface FeatureFlags {
  enableParticles: boolean;
  enableAnimations: boolean;
  enableSounds: boolean;
  enableAnalytics: boolean;
  debugMode: boolean;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  stack?: string;
  timestamp: Date;
}

// Context Types
export interface AppContextType {
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
  activeSection: string;
  setActiveSection: (section: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  performance: PerformanceMetrics;
  featureFlags: FeatureFlags;
}