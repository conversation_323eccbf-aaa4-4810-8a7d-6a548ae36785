// Application configuration and constants
export const APP_CONFIG = {
  // Personal Information
  personal: {
    name: '<PERSON>',
    username: 'vicky_mosafan',
    title: 'Full-Stack Developer & Tech Enthusiast',
    location: 'Indonesia',
    experience: '2+ Years',
    email: '<EMAIL>',
    status: 'AVAILABLE FOR HIRE',
  },

  // Technical Stack
  techStack: {
    frontend: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],
    backend: ['Node.js', 'Express', 'Python', 'REST APIs'],
    database: ['MongoDB', 'PostgreSQL', 'Redis'],
    tools: ['Git', 'Docker', 'AWS', 'Vercel'],
  },

  // Social Links
  social: {
    github: 'https://github.com/vickymosafan',
    linkedin: 'https://linkedin.com/in/vickymosafan',
    twitter: 'https://twitter.com/vickymosafan',
    portfolio: 'https://vickymosafan.dev',
  },

  // Analytics/Stats
  analytics: {
    responseTime: '< 24h',
    projectsCompleted: '15+',
    yearsExperience: '2+ Yrs',
    codeQuality: 'AAA+',
  },

  // Terminal Commands for Hero Section
  terminalCommands: [
    { command: 'whoami', output: 'vicky-mosafan' },
    { command: 'cat /etc/profile', output: 'Full-Stack Developer & Tech Enthusiast' },
    { command: 'ls -la ~/skills/', output: 'react/ nodejs/ python/ typescript/ nextjs/' },
    { command: 'git log --oneline', output: '* 15+ projects delivered successfully\\n* Clean code advocate\\n* Performance optimization expert' },
    { command: 'docker ps', output: 'CONTAINER     STATUS      PORTS\\nvicky-portfolio  Up 2 years   0.0.0.0:3000->3000/tcp' },
    { command: 'curl -X GET /api/vicky/status', output: '{\"status\": \"available\", \"location\": \"Indonesia\", \"experience\": \"2+ years\"}' },
    { command: 'npm run --version', output: 'Building amazing things since 2020 🚀' }
  ],

  // Animation Settings
  animations: {
    // Timing
    durations: {
      fast: 0.2,
      normal: 0.3,
      slow: 0.5,
      typing: 50, // milliseconds per character
    },
    
    // Easing
    easings: {
      smooth: [0.25, 0.46, 0.45, 0.94],
      bounce: [0.68, -0.55, 0.265, 1.55],
      elastic: [0.25, 0.46, 0.45, 0.94],
    },

    // Spring configs
    springs: {
      gentle: { stiffness: 100, damping: 30 },
      snappy: { stiffness: 300, damping: 30 },
      wobbly: { stiffness: 180, damping: 12 },
    },

    // Delays
    stagger: 0.1,
    pageTransition: 0.3,
  },

  // Theme Configuration
  theme: {
    colors: {
      primary: '#22c55e', // green-500
      secondary: '#3b82f6', // blue-500
      accent: '#a855f7', // purple-500
      warning: '#f59e0b', // amber-500
      danger: '#ef4444', // red-500
      success: '#10b981', // emerald-500
    },
    
    gradients: {
      hero: 'from-gray-900 via-gray-800 to-black',
      accent: 'from-green-500 to-blue-500',
      card: 'from-gray-800 to-gray-900',
    },

    shadows: {
      glow: '0 0 20px rgba(34, 197, 94, 0.3)',
      card: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    },
  },

  // Responsive Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Performance Settings
  performance: {
    lazyLoadThreshold: 100, // pixels
    animationThreshold: 60, // fps
    particleCount: {
      mobile: 20,
      tablet: 30,
      desktop: 50,
    },
  },

  // Contact Information
  contact: {
    email: '<EMAIL>',
    subject: 'Let\'s work together!',
    message: 'Hi Vicky! I\'d love to discuss a project with you.',
  },
} as const;

// Type definitions for configuration
export type AppConfig = typeof APP_CONFIG;
export type TerminalCommand = typeof APP_CONFIG.terminalCommands[0];
export type TechStackCategory = keyof typeof APP_CONFIG.techStack;