import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { APP_CONFIG } from './config';

// Utility function for class name merging
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Animation utilities
export const animations = {
  // Fade animations
  fadeIn: (delay = 0) => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  fadeInUp: (delay = 0) => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  fadeInDown: (delay = 0) => ({
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  fadeInLeft: (delay = 0) => ({
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  fadeInRight: (delay = 0) => ({
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  // Scale animations
  scaleIn: (delay = 0) => ({
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: APP_CONFIG.animations.durations.normal, delay }
  }),

  // Stagger animation helper
  stagger: (children: number, baseDelay = 0) => {
    return Array.from({ length: children }, (_, i) => ({
      delay: baseDelay + i * APP_CONFIG.animations.stagger
    }));
  },

  // Hover effects
  hover: {
    lift: { y: -2, scale: 1.02 },
    glow: { boxShadow: APP_CONFIG.theme.shadows.glow },
    scale: { scale: 1.05 },
    rotate: { rotate: [0, -2, 2, 0] },
  },

  // Tap effects
  tap: {
    scale: { scale: 0.95 },
    push: { scale: 0.98, y: 1 },
  },

  // Spring configurations
  springs: APP_CONFIG.animations.springs,
};

// Scroll utilities
export const scrollUtils = {
  // Smooth scroll to element
  scrollToElement: (elementId: string, offset = 0) => {
    const element = document.getElementById(elementId);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  },

  // Get scroll progress for element
  getScrollProgress: (elementId: string) => {
    const element = document.getElementById(elementId);
    if (!element) return 0;

    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const elementHeight = rect.height;
    
    const elementTop = rect.top;
    const elementBottom = rect.bottom;

    if (elementBottom < 0 || elementTop > windowHeight) {
      return elementTop > windowHeight ? 0 : 1;
    }

    const visibleHeight = Math.min(elementBottom, windowHeight) - Math.max(elementTop, 0);
    return visibleHeight / Math.min(elementHeight, windowHeight);
  },

  // Get current section based on scroll position
  getCurrentSection: (sections: string[], offset = 100) => {
    const scrollPosition = window.scrollY + offset;

    for (const sectionId of sections) {
      const element = document.getElementById(sectionId);
      if (element) {
        const offsetTop = element.offsetTop;
        const offsetBottom = offsetTop + element.offsetHeight;

        if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
          return sectionId;
        }
      }
    }

    return sections[0];
  },
};

// Performance utilities
export const performanceUtils = {
  // Throttle function
  throttle: (func: (...args: any[]) => any, limit: number) => {
    let inThrottle: boolean;
    return function (this: any, ...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  },

  // Debounce function
  debounce: (func: (...args: any[]) => any, wait: number) => {
    let timeout: NodeJS.Timeout;
    return function (this: any, ...args: any[]) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  },

  // Request animation frame with fallback
  requestAnimationFrame: (callback: () => void) => {
    if (typeof window !== 'undefined' && window.requestAnimationFrame) {
      return window.requestAnimationFrame(callback);
    } else {
      return setTimeout(callback, 16); // ~60fps fallback
    }
  },

  // Cancel animation frame with fallback
  cancelAnimationFrame: (id: number) => {
    if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
      window.cancelAnimationFrame(id);
    } else {
      clearTimeout(id);
    }
  },

  // Get device performance tier
  getPerformanceTier: () => {
    if (typeof window === 'undefined') return 'medium';
    
    const connection = (navigator as any).connection;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const memory = (performance as any).memory?.usedJSHeapSize || 0;

    // High performance: many cores, good connection, lots of memory
    if (hardwareConcurrency >= 8 && (!connection || connection.effectiveType === '4g') && memory < 50000000) {
      return 'high';
    }
    
    // Low performance: few cores, slow connection, limited memory
    if (hardwareConcurrency <= 2 || (connection && connection.effectiveType === '2g') || memory > 100000000) {
      return 'low';
    }

    return 'medium';
  },
};

// Color utilities
export const colorUtils = {
  // Convert hex to rgba
  hexToRgba: (hex: string, alpha = 1) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },

  // Get contrasting text color
  getContrastColor: (hexColor: string) => {
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? '#000000' : '#ffffff';
  },

  // Generate gradient
  generateGradient: (color1: string, color2: string, direction = 'to right') => {
    return `linear-gradient(${direction}, ${color1}, ${color2})`;
  },
};

// String utilities
export const stringUtils = {
  // Truncate text
  truncate: (text: string, length: number, suffix = '...') => {
    if (text.length <= length) return text;
    return text.substring(0, length - suffix.length) + suffix;
  },

  // Slugify string
  slugify: (text: string) => {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  // Capitalize first letter
  capitalize: (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1);
  },

  // Format terminal command
  formatCommand: (command: string, prefix = '$') => {
    return `${prefix} ${command}`;
  },
};

// Number utilities
export const numberUtils = {
  // Format number with commas
  formatNumber: (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },

  // Clamp number between min and max
  clamp: (num: number, min: number, max: number) => {
    return Math.min(Math.max(num, min), max);
  },

  // Linear interpolation
  lerp: (start: number, end: number, factor: number) => {
    return start + (end - start) * factor;
  },

  // Map number from one range to another
  mapRange: (value: number, inMin: number, inMax: number, outMin: number, outMax: number) => {
    return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
  },

  // Generate random number between min and max
  random: (min: number, max: number) => {
    return Math.random() * (max - min) + min;
  },

  // Generate random integer between min and max
  randomInt: (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
};

// Date utilities
export const dateUtils = {
  // Format date to readable string
  formatDate: (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  },

  // Get relative time string
  getRelativeTime: (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  },
};

// Storage utilities
export const storageUtils = {
  // Get item from localStorage with error handling
  getItem: (key: string, defaultValue: any) => {
    if (typeof window === 'undefined') return defaultValue;
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  },

  // Set item in localStorage with error handling
  setItem: (key: string, value: any): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch {
      return false;
    }
  },

  // Remove item from localStorage
  removeItem: (key: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      window.localStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  },
};

// Device detection utilities
export const deviceUtils = {
  // Check if mobile device
  isMobile: () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < 768;
  },

  // Check if tablet device
  isTablet: () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth >= 768 && window.innerWidth < 1024;
  },

  // Check if desktop device
  isDesktop: () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth >= 1024;
  },

  // Get device type
  getDeviceType: () => {
    if (deviceUtils.isMobile()) return 'mobile';
    if (deviceUtils.isTablet()) return 'tablet';
    return 'desktop';
  },

  // Check if touch device
  isTouchDevice: () => {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  // Get screen dimensions
  getScreenDimensions: () => {
    if (typeof window === 'undefined') return { width: 0, height: 0 };
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  },
};

// SEO utilities
export const seoUtils = {
  // Generate meta tags
  generateMetaTags: (title: string, description: string, image?: string) => {
    return {
      title,
      description,
      openGraph: {
        title,
        description,
        image,
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        image,
      },
    };
  },

  // Generate JSON-LD structured data
  generateStructuredData: () => {
    return {
      '@context': 'https://schema.org',
      '@type': 'Person',
      name: APP_CONFIG.personal.name,
      jobTitle: APP_CONFIG.personal.title,
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'Indonesia',
      },
      email: APP_CONFIG.personal.email,
      url: typeof window !== 'undefined' ? window.location.origin : '',
    };
  },
};