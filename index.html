<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON> - Fullstack Developer Portfolio. Modern web applications with React, Node.js, and cutting-edge technologies." />
    <meta name="keywords" content="fullstack developer, react, nodejs, typescript, portfolio, web development" />
    <meta name="author" content="Vicky Mosafan" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON> - Fullstack Developer" />
    <meta property="og:description" content="Modern web applications with React, Node.js, and cutting-edge technologies." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://vickymosafan.dev" />
    <meta property="og:image" content="https://vickymosafan.dev/og-image.jpg" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:creator" content="@vickymosafan" />
    <meta name="twitter:title" content="Vicky Mosafan - Fullstack Developer" />
    <meta name="twitter:description" content="Modern web applications with React, Node.js, and cutting-edge technologies." />
    <meta name="twitter:image" content="https://vickymosafan.dev/og-image.jpg" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap" rel="stylesheet" />
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    
    <title>Vicky Mosafan - Fullstack Developer</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/main.tsx"></script>
  </body>
</html>