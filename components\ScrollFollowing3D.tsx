import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'motion/react';

interface ScrollFollowing3DProps {
  className?: string;
  children?: React.ReactNode;
}

export default function ScrollFollowing3D({ className, children }: ScrollFollowing3DProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const rotateX = useTransform(scrollYProgress, [0, 1], [15, -15]);
  const rotateY = useTransform(scrollYProgress, [0, 1], [-10, 10]);

  return (
    <div ref={ref} className={`relative ${className}`}>
      <motion.div
        style={{ y, rotateX, rotateY }}
        className="transform-gpu"
      >
        <div className="relative w-32 h-32 mx-auto">
          {/* Simulated 3D Cube */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/40 rounded-lg shadow-2xl transform rotate-12 animate-pulse" />
          <div className="absolute inset-2 bg-gradient-to-tl from-secondary/30 to-primary/20 rounded-lg shadow-xl transform -rotate-6" />
          <div className="absolute inset-4 bg-gradient-to-br from-accent/40 to-muted/30 rounded-lg shadow-lg transform rotate-3" />
          
          {/* Floating Elements */}
          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 5, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute -top-4 -right-4 w-6 h-6 bg-primary rounded-full shadow-lg"
          />
          <motion.div
            animate={{
              y: [0, 10, 0],
              rotate: [0, -5, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
            className="absolute -bottom-2 -left-2 w-4 h-4 bg-accent rounded-full shadow-md"
          />
          
          {children}
        </div>
      </motion.div>
    </div>
  );
}