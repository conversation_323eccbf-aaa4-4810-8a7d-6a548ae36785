# <PERSON> - Portfolio

A modern, interactive portfolio website built with React, TypeScript, and Framer Motion. Features a terminal-inspired design with 3D animations and smooth scrolling between sections.

## ✨ Features

- **Terminal Theme**: Unique terminal-inspired design with typewriter effects
- **Interactive 3D Elements**: Custom 3D robot and project browser components
- **Smooth Animations**: Powered by Framer Motion for fluid transitions
- **Snap Scrolling**: Seamless navigation between sections
- **Responsive Design**: Mobile-first approach with perfect mobile experience
- **Modern Tech Stack**: Built with latest React, TypeScript, and Tailwind CSS v4
- **Performance Optimized**: Fast loading with code splitting and optimization

## 🚀 Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS v4, Custom CSS
- **Animations**: Framer Motion (motion/react)
- **Icons**: Lucide React
- **Forms**: React Hook Form
- **Notifications**: Sonner
- **UI Components**: shadcn/ui
- **Build Tool**: Vite
- **Code Quality**: <PERSON><PERSON><PERSON>, Prettier, TypeScript

## 📁 Project Structure

```
├── components/              # React components
│   ├── ui/                 # shadcn/ui components
│   ├── figma/              # Figma integration components
│   ├── hooks/              # Custom React hooks
│   ├── AboutSection.tsx    # About section with 3D robot
│   ├── ContactSection.tsx  # Contact form section
│   ├── HeroSection.tsx     # Landing hero section
│   ├── Navigation.tsx      # Navigation component
│   ├── ProjectsSection.tsx # Projects showcase
│   └── SplashScreen.tsx    # Loading screen
├── lib/                    # Utilities and configurations
│   ├── data/              # Data files
│   ├── hooks/             # Custom hooks
│   └── utils.ts           # Utility functions
├── styles/                # CSS files
│   └── globals.css        # Global styles with Tailwind
└── App.tsx                # Main application component
```

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/vickymosafan/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

## 📱 Sections

1. **Home**: Hero section with animated terminal introduction
2. **About**: Personal bio with interactive 3D robot that follows mouse
3. **Projects**: Featured projects with interactive browser component
4. **Contact**: Contact form with terminal-style interface

## 🎨 Design Features

- **Terminal Aesthetic**: Green/blue/cyan color palette with monospace fonts
- **Typewriter Effects**: Natural typing animations throughout
- **3D Interactions**: Mouse-following 3D elements
- **Matrix Effects**: Subtle matrix rain animations
- **Ambient Lighting**: Dynamic lighting effects
- **Smooth Scrolling**: Snap-to-section navigation

## ⚡ Performance

- Code splitting for optimal loading
- Lazy loading of components
- Optimized animations with `transform-gpu`
- Compressed assets and fonts
- Mobile-first responsive design

## 🔧 Customization

### Colors
Update color variables in `styles/globals.css`:
```css
:root {
  --terminal-green: #22c55e;
  --terminal-blue: #3b82f6;
  --terminal-cyan: #06b6d4;
}
```

### Content
Update personal information in:
- `components/AboutSection.tsx` - Bio and skills
- `components/ProjectsSection.tsx` - Project data
- `components/ContactSection.tsx` - Contact information

### Animations
Modify animation settings in component files or add new ones in `tailwind.config.ts`.

## 📄 License

MIT License - feel free to use this project as inspiration for your own portfolio!

## 🤝 Contributing

Contributions, issues, and feature requests are welcome!

## 📬 Contact

- Website: [vickymosafan.dev](https://vickymosafan.dev)
- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/vicky](https://linkedin.com/in/vicky)
- GitHub: [github.com/vicky](https://github.com/vicky)

---

Built with ❤️ using React, TypeScript, and Framer Motion