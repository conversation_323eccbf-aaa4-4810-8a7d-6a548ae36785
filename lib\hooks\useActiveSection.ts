import { useState, useEffect } from 'react';
import { scrollUtils, performanceUtils } from '../utils';

interface UseActiveSectionOptions {
  sections?: string[];
  offset?: number;
  throttle?: number;
}

export function useActiveSection(options: UseActiveSectionOptions = {}) {
  const { 
    sections = ['home'], // Default to just home section
    offset = 100,
    throttle = 16 
  } = options;
  
  const [activeSection, setActiveSection] = useState(sections[0]);

  useEffect(() => {
    const updateActiveSection = performanceUtils.throttle(() => {
      const currentSection = scrollUtils.getCurrentSection(sections, offset);
      if (currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    }, throttle);

    window.addEventListener('scroll', updateActiveSection, { passive: true });
    updateActiveSection(); // Initial call

    return () => window.removeEventListener('scroll', updateActiveSection);
  }, [sections, offset, throttle, activeSection]);

  const scrollToSection = (sectionId: string) => {
    if (sectionId === 'home') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      scrollUtils.scrollToElement(sectionId, offset);
    }
    setActiveSection(sectionId);
  };

  return {
    activeSection,
    setActiveSection,
    scrollToSection,
    sections,
  };
}