import { useState, useEffect, useRef } from 'react';
import { motion } from 'motion/react';
import { Cpu } from 'lucide-react';

const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンVICKY';

// Enhanced Typewriter component with keyword highlighting
const TypewriterText = ({ 
  text, 
  className = '', 
  speed = 40, 
  onComplete,
  startDelay = 0,
  highlightWords = []
}: { 
  text: string; 
  className?: string; 
  speed?: number; 
  onComplete?: () => void;
  startDelay?: number;
  highlightWords?: string[];
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setHasStarted(true);
    }, startDelay);
    
    return () => clearTimeout(startTimer);
  }, [startDelay]);

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete();
      }
      return;
    }

    const char = text[currentIndex];
    let delay = speed;
    
    if (char === '.' || char === '!' || char === '?') delay += 150;
    else if (char === ',' || char === ';') delay += 80;
    else if (char === ' ') delay -= 10;
    
    delay += Math.random() * 30 - 15;

    const timer = setTimeout(() => {
      setDisplayedText(text.slice(0, currentIndex + 1));
      setCurrentIndex(currentIndex + 1);
    }, Math.max(delay, 15));
    
    return () => clearTimeout(timer);
  }, [currentIndex, text, speed, onComplete, hasStarted]);

  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setHasStarted(false);
  }, [text]);

  // Highlight keywords
  const getHighlightedText = (text: string) => {
    if (!highlightWords.length) return text;
    
    let highlightedText = text;
    highlightWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<span class="text-blue-400">$1</span>`);
    });
    
    return highlightedText;
  };

  return (
    <span className={className}>
      <span dangerouslySetInnerHTML={{ __html: getHighlightedText(displayedText) }} />
      {hasStarted && currentIndex < text.length && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 0.8, repeat: Infinity }}
          className="text-green-400"
        >
          |
        </motion.span>
      )}
    </span>
  );
};

// Custom 3D Robot Component
const Custom3DRobot = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  // Mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // Normalize mouse position (-1 to 1)
        const normalizedX = (e.clientX - centerX) / (rect.width / 2);
        const normalizedY = (e.clientY - centerY) / (rect.height / 2);
        
        setMousePosition({
          x: Math.max(-1, Math.min(1, normalizedX)),
          y: Math.max(-1, Math.min(1, normalizedY))
        });
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Calculate eye and head positions based on mouse
  const eyeOffsetX = mousePosition.x * 6;
  const eyeOffsetY = mousePosition.y * 4;
  const headRotationX = mousePosition.y * 10;
  const headRotationY = mousePosition.x * 15;
  const bodyRotationY = mousePosition.x * 5;

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-full flex items-center justify-center perspective-1000"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ perspective: '1000px' }}
    >
      {/* Main Robot Container */}
      <motion.div
        className="relative transform-gpu"
        animate={{
          rotateY: bodyRotationY,
          scale: isHovered ? 1.05 : 1
        }}
        transition={{ type: "spring", stiffness: 150, damping: 20 }}
        style={{
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Robot Base/Stand */}
        <motion.div
          className="relative w-40 h-8 mx-auto mb-6 rounded-full"
          style={{
            background: 'linear-gradient(90deg, #1f2937 0%, #22c55e 20%, #3b82f6 40%, #06b6d4 60%, #22c55e 80%, #1f2937 100%)',
            boxShadow: '0 4px 20px rgba(34, 197, 94, 0.3), inset 0 2px 10px rgba(59, 130, 246, 0.2)'
          }}
          animate={{
            boxShadow: [
              '0 4px 20px rgba(34, 197, 94, 0.3), inset 0 2px 10px rgba(59, 130, 246, 0.2)',
              '0 6px 30px rgba(59, 130, 246, 0.4), inset 0 2px 10px rgba(6, 182, 212, 0.3)',
              '0 4px 20px rgba(34, 197, 94, 0.3), inset 0 2px 10px rgba(59, 130, 246, 0.2)'
            ]
          }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          {/* Base details */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-2 bg-cyan-400 rounded-full opacity-80" />
          <div className="absolute top-1/2 left-1/4 -translate-x-1/2 -translate-y-1/2 w-3 h-1 bg-green-400 rounded-full opacity-60" />
          <div className="absolute top-1/2 right-1/4 translate-x-1/2 -translate-y-1/2 w-3 h-1 bg-blue-400 rounded-full opacity-60" />
        </motion.div>

        {/* Robot Neck/Stand */}
        <motion.div
          className="relative w-8 h-20 mx-auto mb-4 rounded-lg"
          style={{
            background: 'linear-gradient(180deg, #374151 0%, #1f2937 50%, #374151 100%)',
            boxShadow: 'inset 0 2px 10px rgba(0, 0, 0, 0.3), 0 0 15px rgba(34, 197, 94, 0.2)'
          }}
          animate={{
            rotateX: headRotationX * 0.3
          }}
          transition={{ type: "spring", stiffness: 200, damping: 25 }}
        >
          {/* Neck segments */}
          <div className="absolute top-2 left-1/2 -translate-x-1/2 w-6 h-1 bg-green-400 rounded-full opacity-70" />
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 w-6 h-1 bg-blue-400 rounded-full opacity-70" />
          <div className="absolute bottom-2 left-1/2 -translate-x-1/2 w-6 h-1 bg-cyan-400 rounded-full opacity-70" />
        </motion.div>

        {/* Robot Body */}
        <motion.div
          className="relative w-40 h-48 mx-auto mb-6 rounded-2xl border-2 border-green-400/30"
          style={{
            background: 'linear-gradient(135deg, #1f2937 0%, #22c55e 25%, #3b82f6 50%, #06b6d4 75%, #1f2937 100%)',
            boxShadow: '0 8px 32px rgba(34, 197, 94, 0.3), inset 0 4px 20px rgba(59, 130, 246, 0.2)'
          }}
          animate={{
            boxShadow: [
              '0 8px 32px rgba(34, 197, 94, 0.3), inset 0 4px 20px rgba(59, 130, 246, 0.2)',
              '0 12px 40px rgba(59, 130, 246, 0.4), inset 0 4px 20px rgba(6, 182, 212, 0.3)',
              '0 8px 32px rgba(34, 197, 94, 0.3), inset 0 4px 20px rgba(59, 130, 246, 0.2)'
            ]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          {/* Body Details */}
          <div className="absolute top-6 left-1/2 -translate-x-1/2 w-24 h-3 bg-cyan-400 rounded-full opacity-80" />
          <div className="absolute top-12 left-1/2 -translate-x-1/2 w-20 h-2 bg-green-300 rounded-full opacity-60" />
          
          {/* Central Power Core */}
          <motion.div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full border-2 border-white/20"
            style={{
              background: 'radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(34, 197, 94, 0.8) 40%, rgba(59, 130, 246, 0.6) 100%)'
            }}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.8, 1, 0.8],
              boxShadow: [
                '0 0 20px rgba(255, 255, 255, 0.5)',
                '0 0 40px rgba(34, 197, 94, 0.8)',
                '0 0 20px rgba(255, 255, 255, 0.5)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-white opacity-90" />
          </motion.div>

          {/* Side Panels */}
          <div className="absolute left-2 top-1/3 w-4 h-16 bg-gradient-to-b from-green-400 to-cyan-400 rounded-r-lg opacity-80" />
          <div className="absolute right-2 top-1/3 w-4 h-16 bg-gradient-to-b from-blue-400 to-cyan-400 rounded-l-lg opacity-80" />
          
          {/* Bottom Vents */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-28 h-4 bg-gray-700 rounded-lg border border-cyan-400/50">
            <div className="flex justify-evenly items-center h-full px-2">
              <div className="w-1 h-2 bg-cyan-400 rounded-full opacity-80" />
              <div className="w-1 h-2 bg-green-400 rounded-full opacity-80" />
              <div className="w-1 h-2 bg-blue-400 rounded-full opacity-80" />
              <div className="w-1 h-2 bg-cyan-400 rounded-full opacity-80" />
              <div className="w-1 h-2 bg-green-400 rounded-full opacity-80" />
            </div>
          </div>

          {/* Arms */}
          <div className="absolute left-[-20px] top-8 w-8 h-24 bg-gradient-to-b from-green-400 to-cyan-400 rounded-lg border border-green-300 opacity-80 transform rotate-12" />
          <div className="absolute right-[-20px] top-8 w-8 h-24 bg-gradient-to-b from-blue-400 to-cyan-400 rounded-lg border border-blue-300 opacity-80 transform -rotate-12" />
        </motion.div>

        {/* Robot Head */}
        <motion.div
          className="relative w-48 h-32 mx-auto rounded-2xl border-2 border-green-400/40"
          style={{
            background: 'linear-gradient(45deg, #1f2937 0%, #374151 25%, #22c55e 50%, #3b82f6 75%, #1f2937 100%)',
            boxShadow: '0 6px 25px rgba(34, 197, 94, 0.4), inset 0 3px 15px rgba(59, 130, 246, 0.3)'
          }}
          animate={{
            rotateX: headRotationX,
            rotateY: headRotationY
          }}
          transition={{ type: "spring", stiffness: 200, damping: 25 }}
        >
          {/* Head Top Light Bar */}
          <motion.div
            className="absolute -top-3 left-1/2 -translate-x-1/2 w-32 h-4 rounded-full border border-cyan-400/50"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(6, 182, 212, 0.8) 50%, transparent 100%)'
            }}
            animate={{
              opacity: [0.6, 1, 0.6],
              boxShadow: [
                '0 0 15px rgba(6, 182, 212, 0.5)',
                '0 0 30px rgba(6, 182, 212, 0.8)',
                '0 0 15px rgba(6, 182, 212, 0.5)'
              ]
            }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />

          {/* Eyes Container */}
          <div className="absolute top-8 left-1/2 -translate-x-1/2 flex gap-8">
            {/* Left Eye */}
            <motion.div
              className="relative w-12 h-12 rounded-full border-2 border-green-300"
              style={{
                background: 'radial-gradient(circle, #1f2937 0%, #374151 70%)'
              }}
              animate={{
                scale: isHovered ? [1, 1.1, 1] : 1
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute w-8 h-8 rounded-full bg-white"
                style={{
                  left: `calc(50% + ${eyeOffsetX}px)`,
                  top: `calc(50% + ${eyeOffsetY}px)`,
                  transform: 'translate(-50%, -50%)',
                  boxShadow: '0 0 20px rgba(255, 255, 255, 0.8)'
                }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-green-400" />
                <motion.div
                  className="absolute top-2 left-2 w-2 h-2 rounded-full bg-white opacity-80"
                  animate={{
                    opacity: [0.8, 1, 0.8]
                  }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              </motion.div>
            </motion.div>

            {/* Right Eye */}
            <motion.div
              className="relative w-12 h-12 rounded-full border-2 border-green-300"
              style={{
                background: 'radial-gradient(circle, #1f2937 0%, #374151 70%)'
              }}
              animate={{
                scale: isHovered ? [1, 1.1, 1] : 1
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute w-8 h-8 rounded-full bg-white"
                style={{
                  left: `calc(50% + ${eyeOffsetX}px)`,
                  top: `calc(50% + ${eyeOffsetY}px)`,
                  transform: 'translate(-50%, -50%)',
                  boxShadow: '0 0 20px rgba(255, 255, 255, 0.8)'
                }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-blue-400" />
                <motion.div
                  className="absolute top-2 left-2 w-2 h-2 rounded-full bg-white opacity-80"
                  animate={{
                    opacity: [0.8, 1, 0.8]
                  }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              </motion.div>
            </motion.div>
          </div>

          {/* Head Side Antenna */}
          <div className="absolute left-2 top-4 w-3 h-12 bg-green-400 rounded-r-lg opacity-70" />
          <div className="absolute right-2 top-4 w-3 h-12 bg-blue-400 rounded-l-lg opacity-70" />

          {/* Head Bottom Display */}
          <motion.div
            className="absolute bottom-4 left-1/2 -translate-x-1/2 w-24 h-6 rounded-lg border border-cyan-400"
            style={{
              background: 'linear-gradient(90deg, #1f2937 0%, #22c55e 50%, #1f2937 100%)'
            }}
            animate={{
              opacity: [0.7, 1, 0.7],
              boxShadow: [
                '0 0 10px rgba(6, 182, 212, 0.3)',
                '0 0 20px rgba(34, 197, 94, 0.6)',
                '0 0 10px rgba(6, 182, 212, 0.3)'
              ]
            }}
            transition={{ duration: 2.5, repeat: Infinity }}
          >
            {/* Display segments */}
            <div className="flex justify-evenly items-center h-full px-2">
              <div className="w-1 h-2 bg-cyan-400 rounded-full opacity-80" />
              <div className="w-2 h-3 bg-green-400 rounded-full opacity-90" />
              <div className="w-1 h-2 bg-blue-400 rounded-full opacity-80" />
              <div className="w-2 h-3 bg-cyan-400 rounded-full opacity-90" />
              <div className="w-1 h-2 bg-green-400 rounded-full opacity-80" />
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Background Matrix Rain */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <div className="absolute inset-0 grid grid-cols-8 gap-1">
          {Array.from({ length: 30 }).map((_, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, 500],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "linear"
              }}
              className="text-green-400 font-mono text-xs"
            >
              {matrixChars[Math.floor(Math.random() * matrixChars.length)]}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced Ambient Lighting Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-48 h-48 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(34, 197, 94, 0.15) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-56 h-56 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.12) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1.2, 0.8, 1.2],
            opacity: [0.2, 0.6, 0.2]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
        <motion.div
          className="absolute top-1/2 right-1/3 w-40 h-40 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 70%)',
          }}
          animate={{
            scale: [0.8, 1.3, 0.8],
            opacity: [0.4, 0.8, 0.4]
          }}
          transition={{ duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 2 }}
        />
      </div>

      {/* Terminal Grid Overlay */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(rgba(6, 182, 212, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(6, 182, 212, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px, 60px 60px, 20px 20px, 20px 20px'
          }}
        />
      </div>
    </div>
  );
};

export default function AboutSection() {
  const [currentBioIndex, setBioIndex] = useState(0);

  const bioCommands = [
    {
      command: "$ grep -i 'developer' /usr/vicky/profile.txt",
      output: "Hello! I'm Vicky Mosafan, a passionate fullstack developer with 5+ years of experience building scalable web applications that solve real-world problems.",
      keywords: ["passionate", "scalable"]
    },
    {
      command: "$ cat /usr/vicky/expertise.md | head -3",
      output: "I specialize in crafting innovative solutions from concept to deployment, with expertise in modern frameworks and cutting-edge technologies.",
      keywords: ["innovative", "modern", "cutting-edge"]
    },
    {
      command: "$ find /usr/vicky/skills -type f -name '*.tech'",
      output: "Frontend: React, Next.js, TypeScript, Vue.js\nBackend: Node.js, Express, Python, Laravel\nDatabase: PostgreSQL, MongoDB, MySQL\nCloud: AWS, Docker, Kubernetes",
      keywords: ["React", "Node.js", "PostgreSQL", "AWS"]
    },
    {
      command: "$ tail -f /usr/vicky/philosophy.log",
      output: "I believe in clean code, user-centric design, and the power of collaboration. Every project is an opportunity to push boundaries and create something extraordinary.",
      keywords: ["clean code", "user-centric", "extraordinary"]
    }
  ];

  const handleBioComplete = () => {
    if (currentBioIndex < bioCommands.length - 1) {
      setTimeout(() => {
        setBioIndex(prev => prev + 1);
      }, 2000);
    }
  };

  return (
    <section
      id="about"
      className="h-screen w-screen relative overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black text-white"
    >
      {/* Futuristic Grid Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px)
          `,
          backgroundSize: '100px 100px, 100px 100px, 20px 20px, 20px 20px'
        }} />
      </div>

      {/* Main Content Container */}
      <div className="relative h-full w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-12 gap-8 h-full items-center">
          
          {/* Left Column - Bio Code */}
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="lg:col-span-5 space-y-6 font-mono"
          >
            {/* Section Title */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                >
                  <Cpu className="w-8 h-8 text-green-400" />
                </motion.div>
                <h2 className="text-3xl lg:text-4xl font-bold text-green-400">
                  <TypewriterText 
                    text="$ whoami --verbose"
                    speed={100}
                    startDelay={800}
                  />
                </h2>
              </div>
              
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 2, duration: 1 }}
                className="h-px bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 max-w-md origin-left"
              />
            </motion.div>

            {/* Bio Commands Display */}
            <div className="space-y-8">
              {/* Display completed bio commands */}
              {bioCommands.slice(0, currentBioIndex).map((bio, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    {bio.command}
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <span dangerouslySetInnerHTML={{
                      __html: bio.output.replace(
                        new RegExp(`\\b(${bio.keywords.join('|')})\\b`, 'gi'),
                        '<span class="text-blue-400 glow-blue">$1</span>'
                      )
                    }} />
                  </div>
                </motion.div>
              ))}
              
              {/* Current typing bio command */}
              {currentBioIndex < bioCommands.length && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    <TypewriterText 
                      text={bioCommands[currentBioIndex].command}
                      speed={40}
                      startDelay={3000}
                    />
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <TypewriterText 
                      text={bioCommands[currentBioIndex].output}
                      speed={30}
                      startDelay={5000}
                      onComplete={handleBioComplete}
                      highlightWords={bioCommands[currentBioIndex].keywords}
                    />
                  </div>
                </motion.div>
              )}
            </div>

            {/* Status Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 4 }}
              className="flex items-center gap-3 text-sm text-gray-400"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-2 h-2 bg-green-400 rounded-full"
              />
              <span>System: Online | Process: {currentBioIndex + 1}/{bioCommands.length}</span>
            </motion.div>
          </motion.div>

          {/* Center Divider */}
          <motion.div
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ delay: 1, duration: 1.5 }}
            className="hidden lg:block lg:col-span-1 h-full flex items-center justify-center"
          >
            <motion.div
              animate={{ 
                opacity: [0.3, 1, 0.3],
                boxShadow: [
                  '0 0 20px rgba(34, 197, 94, 0.3)',
                  '0 0 40px rgba(59, 130, 246, 0.6)',
                  '0 0 20px rgba(6, 182, 212, 0.4)'
                ]
              }}
              transition={{ duration: 4, repeat: Infinity }}
              className="w-px h-4/5 bg-gradient-to-b from-transparent via-green-400 to-transparent"
            />
          </motion.div>

          {/* Right Column - Custom 3D Robot */}
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            className="lg:col-span-6 h-full flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.3, rotateY: 90 }}
              animate={{ scale: 1, rotateY: 0 }}
              transition={{ delay: 1.5, duration: 1.2, ease: "easeOut" }}
              className="relative w-full h-full"
            >
              <Custom3DRobot />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}