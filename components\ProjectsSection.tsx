import { useState, useEffect, useRef } from 'react';
import { motion } from 'motion/react';
import { FolderGit2, ExternalLink, Github, Globe, Database, Smartphone, Palette } from 'lucide-react';

const matrixChars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンVICKY';

// Enhanced Typewriter component with keyword highlighting
const TypewriterText = ({ 
  text, 
  className = '', 
  speed = 40, 
  onComplete,
  startDelay = 0,
  highlightWords = []
}: { 
  text: string; 
  className?: string; 
  speed?: number; 
  onComplete?: () => void;
  startDelay?: number;
  highlightWords?: string[];
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setHasStarted(true);
    }, startDelay);
    
    return () => clearTimeout(startTimer);
  }, [startDelay]);

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete();
      }
      return;
    }

    const char = text[currentIndex];
    let delay = speed;
    
    if (char === '.' || char === '!' || char === '?') delay += 150;
    else if (char === ',' || char === ';') delay += 80;
    else if (char === ' ') delay -= 10;
    
    delay += Math.random() * 30 - 15;

    const timer = setTimeout(() => {
      setDisplayedText(text.slice(0, currentIndex + 1));
      setCurrentIndex(currentIndex + 1);
    }, Math.max(delay, 15));
    
    return () => clearTimeout(timer);
  }, [currentIndex, text, speed, onComplete, hasStarted]);

  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setHasStarted(false);
  }, [text]);

  // Highlight keywords
  const getHighlightedText = (text: string) => {
    if (!highlightWords.length) return text;
    
    let highlightedText = text;
    highlightWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<span class="text-blue-400">$1</span>`);
    });
    
    return highlightedText;
  };

  return (
    <span className={className}>
      <span dangerouslySetInnerHTML={{ __html: getHighlightedText(displayedText) }} />
      {hasStarted && currentIndex < text.length && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 0.8, repeat: Infinity }}
          className="text-green-400"
        >
          |
        </motion.span>
      )}
    </span>
  );
};

// Interactive Project Browser Component
const ProjectBrowser = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [activeProject, setActiveProject] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const projects = [
    {
      id: 1,
      name: "E-Commerce Platform",
      icon: <Globe className="w-6 h-6" />,
      color: "from-green-400 to-cyan-400",
      description: "Full-stack e-commerce solution with React, Node.js, and PostgreSQL",
      tech: ["React", "Node.js", "PostgreSQL", "Stripe"],
      status: "Live"
    },
    {
      id: 2,
      name: "Mobile Banking App",
      icon: <Smartphone className="w-6 h-6" />,
      color: "from-blue-400 to-purple-400",
      description: "Secure mobile banking application with biometric authentication",
      tech: ["React Native", "Express", "MongoDB", "JWT"],
      status: "Development"
    },
    {
      id: 3,
      name: "Data Analytics Dashboard",
      icon: <Database className="w-6 h-6" />,
      color: "from-purple-400 to-pink-400",
      description: "Real-time analytics dashboard with interactive visualizations",
      tech: ["Vue.js", "Python", "Redis", "D3.js"],
      status: "Live"
    },
    {
      id: 4,
      name: "Design System",
      icon: <Palette className="w-6 h-6" />,
      color: "from-cyan-400 to-blue-400",
      description: "Comprehensive design system and component library",
      tech: ["Storybook", "TypeScript", "Tailwind", "Figma"],
      status: "Open Source"
    }
  ];

  // Auto-cycle through projects
  useEffect(() => {
    if (!isHovered) {
      const interval = setInterval(() => {
        setActiveProject(prev => (prev + 1) % projects.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isHovered, projects.length]);

  const activeProjectData = projects[activeProject];

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-full flex items-center justify-center perspective-1000"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ perspective: '1000px' }}
    >
      {/* Main Project Browser Container */}
      <motion.div
        className="relative transform-gpu w-full max-w-lg"
        animate={{
          scale: isHovered ? 1.02 : 1
        }}
        transition={{ type: "spring", stiffness: 150, damping: 20 }}
        style={{
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Browser Frame */}
        <motion.div
          className="relative bg-gray-900/90 border border-green-400/30 rounded-xl shadow-2xl overflow-hidden backdrop-blur-sm"
          animate={{
            boxShadow: [
              '0 8px 32px rgba(34, 197, 94, 0.3)',
              '0 12px 40px rgba(59, 130, 246, 0.4)',
              '0 8px 32px rgba(34, 197, 94, 0.3)'
            ]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          {/* Browser Header */}
          <div className="bg-gray-800/90 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full" />
                <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                <div className="w-3 h-3 bg-green-500 rounded-full" />
              </div>
              <div className="flex items-center gap-2">
                <FolderGit2 className="w-4 h-4 text-green-400" />
                <span className="text-sm font-mono text-gray-300">
                  ~/projects/{activeProjectData.name.toLowerCase().replace(/\s+/g, '-')}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full"
              />
            </div>
          </div>

          {/* Project Content */}
          <div className="p-6 space-y-6">
            {/* Project Header */}
            <motion.div
              key={activeProject}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-4">
                <motion.div
                  className={`p-3 rounded-lg bg-gradient-to-r ${activeProjectData.color}`}
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  {activeProjectData.icon}
                </motion.div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white font-mono">
                    {activeProjectData.name}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className={`w-2 h-2 rounded-full ${
                        activeProjectData.status === 'Live' ? 'bg-green-400' :
                        activeProjectData.status === 'Development' ? 'bg-yellow-400' :
                        'bg-blue-400'
                      }`}
                    />
                    <span className="text-sm text-gray-400 font-mono">
                      {activeProjectData.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Project Description */}
              <p className="text-gray-300 leading-relaxed font-mono">
                {activeProjectData.description}
              </p>

              {/* Tech Stack */}
              <div className="space-y-2">
                <div className="text-sm text-gray-400 font-mono">Tech Stack:</div>
                <div className="flex flex-wrap gap-2">
                  {activeProjectData.tech.map((tech, index) => (
                    <motion.span
                      key={tech}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="px-3 py-1 bg-gray-800 border border-cyan-400/30 rounded-full text-xs font-mono text-cyan-400"
                    >
                      {tech}
                    </motion.span>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500/20 border border-green-500/50 rounded-lg text-green-400 font-mono text-sm hover:bg-green-500/30 transition-colors"
                >
                  <Github className="w-4 h-4" />
                  Code
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500/20 border border-blue-500/50 rounded-lg text-blue-400 font-mono text-sm hover:bg-blue-500/30 transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  Demo
                </motion.button>
              </div>
            </motion.div>
          </div>

          {/* Project Navigation Dots */}
          <div className="px-6 pb-4">
            <div className="flex justify-center gap-2">
              {projects.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => setActiveProject(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === activeProject
                      ? 'bg-green-400 scale-125'
                      : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Background Matrix Rain */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <div className="absolute inset-0 grid grid-cols-8 gap-1">
          {Array.from({ length: 30 }).map((_, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, 500],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "linear"
              }}
              className="text-green-400 font-mono text-xs"
            >
              {matrixChars[Math.floor(Math.random() * matrixChars.length)]}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced Ambient Lighting Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-48 h-48 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(34, 197, 94, 0.15) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-56 h-56 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.12) 0%, transparent 70%)',
          }}
          animate={{
            scale: [1.2, 0.8, 1.2],
            opacity: [0.2, 0.6, 0.2]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
      </div>

      {/* Terminal Grid Overlay */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
              linear-gradient(rgba(6, 182, 212, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(6, 182, 212, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px, 60px 60px, 20px 20px, 20px 20px'
          }}
        />
      </div>
    </div>
  );
};

export default function ProjectsSection() {
  const [currentProjectIndex, setProjectIndex] = useState(0);

  const projectCommands = [
    {
      command: "$ ls -la ~/projects/featured/",
      output: "Showcasing my most impactful full-stack projects that demonstrate expertise in modern web technologies and problem-solving.",
      keywords: ["impactful", "full-stack", "modern"]
    },
    {
      command: "$ cat ~/projects/stack.md",
      output: "Built with cutting-edge technologies including React, Node.js, TypeScript, and cloud infrastructure for scalable solutions.",
      keywords: ["cutting-edge", "React", "Node.js", "scalable"]
    },
    {
      command: "$ grep -r 'innovation' ~/projects/*/README.md",
      output: "Each project represents a unique challenge solved through innovative thinking and collaborative development.",
      keywords: ["innovation", "challenge", "collaborative"]
    },
    {
      command: "$ find ~/projects -name '*.live' -type f",
      output: "Multiple projects successfully deployed to production, serving real users and solving real-world problems.",
      keywords: ["deployed", "production", "real-world"]
    }
  ];

  const handleProjectComplete = () => {
    if (currentProjectIndex < projectCommands.length - 1) {
      setTimeout(() => {
        setProjectIndex(prev => prev + 1);
      }, 2000);
    }
  };

  return (
    <section
      id="projects"
      className="h-screen w-screen relative overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black text-white"
    >
      {/* Futuristic Grid Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 197, 94, 0.3) 1px, transparent 1px),
            linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px)
          `,
          backgroundSize: '100px 100px, 100px 100px, 20px 20px, 20px 20px'
        }} />
      </div>

      {/* Main Content Container */}
      <div className="relative h-full w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-12 gap-8 h-full items-center">
          
          {/* Left Column - Project Commands */}
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="lg:col-span-5 space-y-6 font-mono"
          >
            {/* Section Title */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="space-y-4"
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                >
                  <FolderGit2 className="w-8 h-8 text-green-400" />
                </motion.div>
                <h2 className="text-3xl lg:text-4xl font-bold text-green-400">
                  <TypewriterText 
                    text="$ ls ~/projects"
                    speed={100}
                    startDelay={800}
                  />
                </h2>
              </div>
              
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 2, duration: 1 }}
                className="h-px bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 max-w-md origin-left"
              />
            </motion.div>

            {/* Project Commands Display */}
            <div className="space-y-8">
              {/* Display completed project commands */}
              {projectCommands.slice(0, currentProjectIndex).map((project, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    {project.command}
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <span dangerouslySetInnerHTML={{
                      __html: project.output.replace(
                        new RegExp(`\\b(${project.keywords.join('|')})\\b`, 'gi'),
                        '<span class="text-blue-400 glow-blue">$1</span>'
                      )
                    }} />
                  </div>
                </motion.div>
              ))}
              
              {/* Current typing project command */}
              {currentProjectIndex < projectCommands.length && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-3"
                >
                  <div className="text-gray-400 text-sm">
                    <TypewriterText 
                      text={projectCommands[currentProjectIndex].command}
                      speed={40}
                      startDelay={3000}
                    />
                  </div>
                  <div className="text-gray-100 leading-relaxed whitespace-pre-wrap pl-4 border-l-2 border-green-400/30">
                    <TypewriterText 
                      text={projectCommands[currentProjectIndex].output}
                      speed={30}
                      startDelay={5000}
                      onComplete={handleProjectComplete}
                      highlightWords={projectCommands[currentProjectIndex].keywords}
                    />
                  </div>
                </motion.div>
              )}
            </div>

            {/* Status Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 4 }}
              className="flex items-center gap-3 text-sm text-gray-400"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-2 h-2 bg-green-400 rounded-full"
              />
              <span>Repository: Active | Showcase: {currentProjectIndex + 1}/{projectCommands.length}</span>
            </motion.div>
          </motion.div>

          {/* Center Divider */}
          <motion.div
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ delay: 1, duration: 1.5 }}
            className="hidden lg:block lg:col-span-1 h-full flex items-center justify-center"
          >
            <motion.div
              animate={{ 
                opacity: [0.3, 1, 0.3],
                boxShadow: [
                  '0 0 20px rgba(34, 197, 94, 0.3)',
                  '0 0 40px rgba(59, 130, 246, 0.6)',
                  '0 0 20px rgba(6, 182, 212, 0.4)'
                ]
              }}
              transition={{ duration: 4, repeat: Infinity }}
              className="w-px h-4/5 bg-gradient-to-b from-transparent via-green-400 to-transparent"
            />
          </motion.div>

          {/* Right Column - Interactive Project Browser */}
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            className="lg:col-span-6 h-full flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.3, rotateY: 90 }}
              animate={{ scale: 1, rotateY: 0 }}
              transition={{ delay: 1.5, duration: 1.2, ease: "easeOut" }}
              className="relative w-full h-full"
            >
              <ProjectBrowser />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}