{"name": "vicky-mosafan-portfolio", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "vicky-mosafan-portfolio", "version": "1.0.0", "license": "MIT", "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.451.0", "motion": "^10.18.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "7.55.0", "sonner": "2.0.3", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^22.8.4", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^4.0.0", "typescript": "^5.6.3", "vite": "^5.4.8", "vite-bundle-analyzer": "^0.11.0", "vite-plugin-eslint": "^1.8.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}, "node_modules/react": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/motion": {"version": "10.18.0", "resolved": "https://registry.npmjs.org/motion/-/motion-10.18.0.tgz", "integrity": "sha512-TUhJTGa4Fv8Aw5VzOr6AoNtXoRVe8UUGlc+PD7Q/iQKoYZMFLKTJB56KIDNBzL7Uqi5B8cFKP8KzCJEKfh6k5Q==", "dependencies": {"@motionone/animation": "^10.18.0", "@motionone/dom": "^10.18.0", "@motionone/svelte": "^10.16.4", "@motionone/types": "^10.17.1", "@motionone/utils": "^10.18.0", "@motionone/vue": "^10.16.4"}}, "node_modules/lucide-react": {"version": "0.451.0", "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.451.0.tgz", "integrity": "sha512-FGTMe9rUKY9SYo2+H4gNS5+fhQCzfRfCU/VQe3L1g3GUDYUTr3iAuAp1h1YkJQiIo1vP3pPoVl3e8yFjnWZJ8Q==", "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc"}}, "node_modules/sonner": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/sonner/-/sonner-2.0.3.tgz", "integrity": "sha512-0GFGwqyNkCa8Q4WdSFQNJqNxRjM3F8TK6mYhK5uh7q0/V1zfp0eD7Cn/RTkYYQHdKuqKJ4sLKoRBWR1N6v8+yA==", "peerDependencies": {"react": "^18.0.0 || ^19.0.0-rc", "react-dom": "^18.0.0 || ^19.0.0-rc"}}, "node_modules/react-hook-form": {"version": "7.55.0", "resolved": "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.55.0.tgz", "integrity": "sha512-k4VLc2+/kQCc3CNmC4J1fBay4QPOlwPNzIZlQNxRyKqz1NhfUt+kXMKhj4+oKuXw7lXGSZkz2ZMFZD1SkC+Zog==", "engines": {"node": ">=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-hook-form"}, "peerDependencies": {"react": "^16.8.0 || ^17 || ^18 || ^19"}}, "node_modules/typescript": {"version": "5.6.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.6.3.tgz", "integrity": "sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/vite": {"version": "5.4.8", "resolved": "https://registry.npmjs.org/vite/-/vite-5.4.8.tgz", "integrity": "sha512-FqrItQ4DT1NC4zCUqMB4c4AZORMKIa0m8/URVCZ77OZ/QSNeJ54bU1vrFADbDsuwfIPcgknRkmqakQcgnL4GiQ==", "dev": true, "dependencies": {"esbuild": "^0.21.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}}, "node_modules/tailwindcss": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.0.0.tgz", "integrity": "sha512-OEPvb2HOoTCOtw4yy0kIjV+MUG1dA26Y3JGHwYRLq1s+t7e1sO2TT5Hs6sHgC4iSCPKrGRGKfaRFNgD6H7M0Fg==", "dev": true, "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.5.3", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.0", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.0", "lilconfig": "^2.1.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.23", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.1", "postcss-nested": "^6.0.1", "postcss-selector-parser": "^6.0.10", "resolve": "^1.22.2", "sucrase": "^3.32.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}}}